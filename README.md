# Potto - Group Wallet App

A financial coordination app where groups can pool money into shared wallets and use individual virtual cards for spending. Perfect for travel, events, group gifts, and shared expenses.

## 🚀 Features

### Core Functionality
- **Group Wallet Creation**: Create named wallets with goals and deadlines
- **Member Invitations**: Invite via email or shareable links
- **Real-time Contributions**: Track funding progress with live updates
- **Virtual Cards**: Individual cards for each member drawing from shared funds
- **Spending Controls**: Set limits and permissions per card
- **Transaction Transparency**: Real-time spend feed showing who, what, where, when
- **Group Communication**: Chat and voting on expenses
- **Wallet Locking**: Automatic locks after events or budget depletion

### Technical Stack
- **Frontend**: Flutter (iOS & Android)
- **Backend**: Supabase (Auth, Database, Real-time)
- **Payments**: Stripe (Processing & Issuing)
- **Notifications**: Firebase Cloud Messaging
- **Database**: PostgreSQL via Supabase

## 🏗️ Architecture

```
├── lib/
│   ├── core/           # Core utilities, constants, themes
│   ├── data/           # Data layer (repositories, models, API clients)
│   ├── domain/         # Business logic (entities, use cases)
│   ├── presentation/   # UI layer (screens, widgets, state management)
│   └── main.dart       # App entry point
├── assets/             # Images, fonts, etc.
├── test/              # Unit and widget tests
└── integration_test/  # Integration tests
```

## 🛠️ Development Setup

### Prerequisites
1. **Flutter SDK** (3.16.0 or later)
2. **Dart SDK** (3.2.0 or later)
3. **Xcode** (for iOS development)
4. **Android Studio** (for Android development)

### Installation Steps

#### 1. Install Flutter
```bash
# Using Homebrew (recommended for macOS)
brew install flutter

# Or download from https://flutter.dev/docs/get-started/install
```

#### 2. Verify Installation
```bash
flutter doctor
```

#### 3. Clone and Setup
```bash
cd potto_app
flutter pub get
```

#### 4. Environment Configuration
Create `.env` file with:
```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
```

## 🚦 Getting Started

### Run the App
```bash
# iOS Simulator
flutter run -d ios

# Android Emulator
flutter run -d android

# Web (for testing)
flutter run -d web
```

### Testing
```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/
```

## 📱 App Flow

1. **Onboarding**: User registration/login via Supabase Auth
2. **Wallet Creation**: Create or join group wallets
3. **Funding**: Members contribute via Stripe payments
4. **Card Issuance**: Virtual cards issued via Stripe Issuing
5. **Spending**: Real-time transaction tracking and notifications
6. **Management**: Group chat, voting, and expense approval

## 🔐 Security & Compliance

- PCI DSS compliance through Stripe
- End-to-end encryption for sensitive data
- Secure authentication via Supabase
- Fraud detection and prevention
- Regular security audits

## 🌟 Future Features

- Premium subscriptions (unlimited wallets, custom branding)
- Physical card shipping
- Currency conversion
- Travel and gift partner integrations
- Advanced analytics and reporting

## 📄 License

This project is proprietary software. All rights reserved.
