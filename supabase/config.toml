[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://potto.app", "potto://auth"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
security_update_password_require_reauthentication = true
enable_signup = true
enable_confirmations = false

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[auth.sms]
enable_signup = false
enable_confirmations = false

[auth.external.apple]
enabled = false

[auth.external.google]
enabled = false

[auth.external.facebook]
enabled = false

[db]
port = 54322
shadow_port = 54320
major_version = 15

[realtime]
enabled = true
ip_version = "ipv4"

[storage]
enabled = true
file_size_limit = "50MiB"
image_transformation = { enabled = true }

[edge_functions]
enabled = true
ip_version = "ipv4"

[analytics]
enabled = false

[functions.potto-webhooks]
verify_jwt = false

[functions.stripe-webhooks]
verify_jwt = false
