-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wallet_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wallet_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- User preferences policies
CREATE POLICY "Users can manage their own preferences" ON public.user_preferences
    FOR ALL USING (auth.uid() = user_id);

-- Wallets policies
CREATE POLICY "Users can view wallets they are members of" ON public.wallets
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = wallets.id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create wallets" ON public.wallets
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Wallet admins can update wallets" ON public.wallets
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = wallets.id 
            AND user_id = auth.uid() 
            AND role = 'admin'
        )
    );

CREATE POLICY "Wallet admins can delete wallets" ON public.wallets
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = wallets.id 
            AND user_id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Wallet settings policies
CREATE POLICY "Wallet members can view settings" ON public.wallet_settings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = wallet_settings.wallet_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Wallet admins can manage settings" ON public.wallet_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = wallet_settings.wallet_id 
            AND user_id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Wallet members policies
CREATE POLICY "Users can view wallet members" ON public.wallet_members
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members wm
            WHERE wm.wallet_id = wallet_members.wallet_id AND wm.user_id = auth.uid()
        )
    );

CREATE POLICY "Wallet admins can manage members" ON public.wallet_members
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = wallet_members.wallet_id 
            AND user_id = auth.uid() 
            AND role = 'admin'
        )
    );

CREATE POLICY "Users can join wallets" ON public.wallet_members
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Invitations policies
CREATE POLICY "Wallet members can view invitations" ON public.invitations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = invitations.wallet_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Wallet members with invite permission can create invitations" ON public.invitations
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = invitations.wallet_id 
            AND user_id = auth.uid() 
            AND (role = 'admin' OR can_invite = true)
        )
    );

CREATE POLICY "Invitation creators can update their invitations" ON public.invitations
    FOR UPDATE USING (auth.uid() = invited_by);

-- Cards policies
CREATE POLICY "Users can view their own cards" ON public.cards
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create cards for wallets they belong to" ON public.cards
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = cards.wallet_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own cards" ON public.cards
    FOR UPDATE USING (auth.uid() = user_id);

-- Transactions policies
CREATE POLICY "Wallet members can view transactions" ON public.transactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = transactions.wallet_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create transactions" ON public.transactions
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = transactions.wallet_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Transaction owners can update their transactions" ON public.transactions
    FOR UPDATE USING (auth.uid() = user_id);

-- Messages policies
CREATE POLICY "Wallet members can view messages" ON public.messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = messages.wallet_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Wallet members can send messages" ON public.messages
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.wallet_members 
            WHERE wallet_id = messages.wallet_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Message authors can update their messages" ON public.messages
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Message authors can delete their messages" ON public.messages
    FOR DELETE USING (auth.uid() = user_id);

-- Notifications policies
CREATE POLICY "Users can manage their own notifications" ON public.notifications
    FOR ALL USING (auth.uid() = user_id);

-- Create functions for automatic user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email)
    VALUES (NEW.id, NEW.email);
    
    INSERT INTO public.user_preferences (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update wallet current amount
CREATE OR REPLACE FUNCTION public.update_wallet_amount()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.type = 'contribution' AND NEW.status = 'completed' THEN
            UPDATE public.wallets 
            SET current_amount = current_amount + NEW.amount,
                updated_at = NOW()
            WHERE id = NEW.wallet_id;
        ELSIF NEW.type = 'spending' AND NEW.status = 'completed' THEN
            UPDATE public.wallets 
            SET current_amount = current_amount - NEW.amount,
                updated_at = NOW()
            WHERE id = NEW.wallet_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Handle status changes
        IF OLD.status != NEW.status THEN
            IF NEW.status = 'completed' THEN
                IF NEW.type = 'contribution' THEN
                    UPDATE public.wallets 
                    SET current_amount = current_amount + NEW.amount,
                        updated_at = NOW()
                    WHERE id = NEW.wallet_id;
                ELSIF NEW.type = 'spending' THEN
                    UPDATE public.wallets 
                    SET current_amount = current_amount - NEW.amount,
                        updated_at = NOW()
                    WHERE id = NEW.wallet_id;
                END IF;
            ELSIF OLD.status = 'completed' AND NEW.status != 'completed' THEN
                -- Reverse the transaction
                IF NEW.type = 'contribution' THEN
                    UPDATE public.wallets 
                    SET current_amount = current_amount - NEW.amount,
                        updated_at = NOW()
                    WHERE id = NEW.wallet_id;
                ELSIF NEW.type = 'spending' THEN
                    UPDATE public.wallets 
                    SET current_amount = current_amount + NEW.amount,
                        updated_at = NOW()
                    WHERE id = NEW.wallet_id;
                END IF;
            END IF;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for wallet amount updates
CREATE TRIGGER on_transaction_change
    AFTER INSERT OR UPDATE ON public.transactions
    FOR EACH ROW EXECUTE FUNCTION public.update_wallet_amount();

-- Create function to update member contributed amount
CREATE OR REPLACE FUNCTION public.update_member_contribution()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.type = 'contribution' AND NEW.status = 'completed' THEN
            UPDATE public.wallet_members 
            SET contributed_amount = contributed_amount + NEW.amount
            WHERE wallet_id = NEW.wallet_id AND user_id = NEW.user_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.status != NEW.status AND NEW.status = 'completed' AND NEW.type = 'contribution' THEN
            UPDATE public.wallet_members 
            SET contributed_amount = contributed_amount + NEW.amount
            WHERE wallet_id = NEW.wallet_id AND user_id = NEW.user_id;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for member contribution updates
CREATE TRIGGER on_contribution_change
    AFTER INSERT OR UPDATE ON public.transactions
    FOR EACH ROW EXECUTE FUNCTION public.update_member_contribution();
