-- Enable realtime for tables that need live updates

-- Enable realtime for wallets (for balance updates, status changes)
ALTER PUBLICATION supabase_realtime ADD TABLE public.wallets;

-- Enable realtime for wallet members (for member changes, role updates)
ALTER PUBLICATION supabase_realtime ADD TABLE public.wallet_members;

-- Enable realtime for transactions (for live transaction feed)
ALTER PUBLICATION supabase_realtime ADD TABLE public.transactions;

-- Enable realtime for messages (for group chat)
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;

-- Enable realtime for notifications (for live notifications)
ALTER PUBLICATION supabase_realtime ADD TABLE public.notifications;

-- Enable realtime for cards (for card status updates)
ALTER PUBLICATION supabase_realtime ADD TABLE public.cards;

-- Enable realtime for invitations (for invitation status updates)
ALTER PUBLICATION supabase_realtime ADD TABLE public.invitations;

-- Create function to broadcast custom events
CREATE OR REPLACE FUNCTION public.broadcast_wallet_update(wallet_id UUID, event_type TEXT, payload JSONB)
RETURNS VOID AS $$
BEGIN
    PERFORM pg_notify(
        'wallet_updates',
        json_build_object(
            'wallet_id', wallet_id,
            'event_type', event_type,
            'payload', payload,
            'timestamp', extract(epoch from now())
        )::text
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to broadcast transaction events
CREATE OR REPLACE FUNCTION public.broadcast_transaction_event()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM public.broadcast_wallet_update(
            NEW.wallet_id,
            'transaction_created',
            json_build_object(
                'transaction_id', NEW.id,
                'type', NEW.type,
                'amount', NEW.amount,
                'user_id', NEW.user_id,
                'status', NEW.status
            )::jsonb
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.status != NEW.status THEN
            PERFORM public.broadcast_wallet_update(
                NEW.wallet_id,
                'transaction_status_changed',
                json_build_object(
                    'transaction_id', NEW.id,
                    'old_status', OLD.status,
                    'new_status', NEW.status,
                    'type', NEW.type,
                    'amount', NEW.amount,
                    'user_id', NEW.user_id
                )::jsonb
            );
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for transaction events
CREATE TRIGGER on_transaction_event
    AFTER INSERT OR UPDATE ON public.transactions
    FOR EACH ROW EXECUTE FUNCTION public.broadcast_transaction_event();

-- Create function to broadcast wallet balance updates
CREATE OR REPLACE FUNCTION public.broadcast_wallet_balance_update()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        IF OLD.current_amount != NEW.current_amount THEN
            PERFORM public.broadcast_wallet_update(
                NEW.id,
                'balance_updated',
                json_build_object(
                    'old_amount', OLD.current_amount,
                    'new_amount', NEW.current_amount,
                    'goal_amount', NEW.goal_amount,
                    'progress_percentage', (NEW.current_amount / NULLIF(NEW.goal_amount, 0)) * 100
                )::jsonb
            );
        END IF;
        
        IF OLD.status != NEW.status THEN
            PERFORM public.broadcast_wallet_update(
                NEW.id,
                'status_changed',
                json_build_object(
                    'old_status', OLD.status,
                    'new_status', NEW.status
                )::jsonb
            );
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for wallet balance updates
CREATE TRIGGER on_wallet_balance_update
    AFTER UPDATE ON public.wallets
    FOR EACH ROW EXECUTE FUNCTION public.broadcast_wallet_balance_update();

-- Create function to broadcast member events
CREATE OR REPLACE FUNCTION public.broadcast_member_event()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM public.broadcast_wallet_update(
            NEW.wallet_id,
            'member_joined',
            json_build_object(
                'user_id', NEW.user_id,
                'role', NEW.role,
                'invited_by', NEW.invited_by
            )::jsonb
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.role != NEW.role THEN
            PERFORM public.broadcast_wallet_update(
                NEW.wallet_id,
                'member_role_changed',
                json_build_object(
                    'user_id', NEW.user_id,
                    'old_role', OLD.role,
                    'new_role', NEW.role
                )::jsonb
            );
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM public.broadcast_wallet_update(
            OLD.wallet_id,
            'member_left',
            json_build_object(
                'user_id', OLD.user_id,
                'role', OLD.role
            )::jsonb
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for member events
CREATE TRIGGER on_member_event
    AFTER INSERT OR UPDATE OR DELETE ON public.wallet_members
    FOR EACH ROW EXECUTE FUNCTION public.broadcast_member_event();

-- Create function to broadcast card events
CREATE OR REPLACE FUNCTION public.broadcast_card_event()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM pg_notify(
            'card_updates',
            json_build_object(
                'event_type', 'card_created',
                'card_id', NEW.id,
                'user_id', NEW.user_id,
                'wallet_id', NEW.wallet_id,
                'status', NEW.status
            )::text
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.status != NEW.status THEN
            PERFORM pg_notify(
                'card_updates',
                json_build_object(
                    'event_type', 'card_status_changed',
                    'card_id', NEW.id,
                    'user_id', NEW.user_id,
                    'wallet_id', NEW.wallet_id,
                    'old_status', OLD.status,
                    'new_status', NEW.status
                )::text
            );
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for card events
CREATE TRIGGER on_card_event
    AFTER INSERT OR UPDATE ON public.cards
    FOR EACH ROW EXECUTE FUNCTION public.broadcast_card_event();

-- Create function to automatically create wallet settings
CREATE OR REPLACE FUNCTION public.create_wallet_settings()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.wallet_settings (wallet_id)
    VALUES (NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic wallet settings creation
CREATE TRIGGER on_wallet_created
    AFTER INSERT ON public.wallets
    FOR EACH ROW EXECUTE FUNCTION public.create_wallet_settings();

-- Create function to automatically add wallet creator as admin
CREATE OR REPLACE FUNCTION public.add_wallet_creator_as_admin()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.wallet_members (user_id, wallet_id, role, can_spend, can_invite)
    VALUES (NEW.created_by, NEW.id, 'admin', true, true);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic admin membership
CREATE TRIGGER on_wallet_created_add_admin
    AFTER INSERT ON public.wallets
    FOR EACH ROW EXECUTE FUNCTION public.add_wallet_creator_as_admin();
