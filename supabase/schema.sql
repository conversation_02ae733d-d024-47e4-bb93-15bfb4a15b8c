-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('admin', 'member', 'viewer');
CREATE TYPE wallet_status AS ENUM ('active', 'locked', 'closed');
CREATE TYPE wallet_type AS ENUM ('travel', 'event', 'gift', 'general');
CREATE TYPE transaction_type AS ENUM ('contribution', 'spending', 'refund', 'fee');
CREATE TYPE transaction_status AS ENUM ('pending', 'completed', 'failed', 'cancelled');
CREATE TYPE card_status AS ENUM ('active', 'inactive', 'blocked', 'expired');
CREATE TYPE card_type AS ENUM ('virtual', 'physical');
CREATE TYPE invitation_status AS ENUM ('pending', 'accepted', 'declined', 'expired');
CREATE TYPE notification_type AS ENUM ('transaction', 'invitation', 'wallet', 'card', 'message');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT,
    last_name TEXT,
    phone_number TEXT,
    avatar_url TEXT,
    is_email_verified BOOLEAN DEFAULT FALSE,
    is_phone_verified BOOLEAN DEFAULT FALSE,
    stripe_customer_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User preferences table
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    notifications_enabled BOOLEAN DEFAULT TRUE,
    email_notifications_enabled BOOLEAN DEFAULT TRUE,
    push_notifications_enabled BOOLEAN DEFAULT TRUE,
    transaction_notifications_enabled BOOLEAN DEFAULT TRUE,
    invitation_notifications_enabled BOOLEAN DEFAULT TRUE,
    currency TEXT DEFAULT 'USD',
    language TEXT DEFAULT 'en',
    timezone TEXT DEFAULT 'UTC',
    biometric_enabled BOOLEAN DEFAULT FALSE,
    dark_mode_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Wallets table
CREATE TABLE public.wallets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    type wallet_type NOT NULL DEFAULT 'general',
    status wallet_status NOT NULL DEFAULT 'active',
    goal_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    current_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'USD',
    deadline TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    image_url TEXT,
    stripe_account_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT positive_goal_amount CHECK (goal_amount >= 0),
    CONSTRAINT positive_current_amount CHECK (current_amount >= 0)
);

-- Wallet settings table
CREATE TABLE public.wallet_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    wallet_id UUID REFERENCES public.wallets(id) ON DELETE CASCADE NOT NULL UNIQUE,
    require_approval_for_spending BOOLEAN DEFAULT FALSE,
    approval_threshold DECIMAL(12,2) DEFAULT 0,
    allow_member_invites BOOLEAN DEFAULT TRUE,
    allow_member_spending BOOLEAN DEFAULT TRUE,
    notify_on_transactions BOOLEAN DEFAULT TRUE,
    notify_on_contributions BOOLEAN DEFAULT TRUE,
    notify_on_invitations BOOLEAN DEFAULT TRUE,
    auto_lock_on_goal_reached BOOLEAN DEFAULT FALSE,
    auto_lock_on_deadline BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Wallet members table
CREATE TABLE public.wallet_members (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    wallet_id UUID REFERENCES public.wallets(id) ON DELETE CASCADE NOT NULL,
    role user_role NOT NULL DEFAULT 'member',
    contributed_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    spending_limit DECIMAL(12,2) NOT NULL DEFAULT 0,
    can_spend BOOLEAN DEFAULT TRUE,
    can_invite BOOLEAN DEFAULT FALSE,
    invited_by UUID REFERENCES public.users(id),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, wallet_id),
    CONSTRAINT positive_contributed_amount CHECK (contributed_amount >= 0),
    CONSTRAINT positive_spending_limit CHECK (spending_limit >= 0)
);

-- Invitations table
CREATE TABLE public.invitations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    wallet_id UUID REFERENCES public.wallets(id) ON DELETE CASCADE NOT NULL,
    invited_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    email TEXT NOT NULL,
    code TEXT NOT NULL UNIQUE,
    status invitation_status NOT NULL DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    accepted_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_expiry CHECK (expires_at > created_at)
);

-- Cards table
CREATE TABLE public.cards (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    wallet_id UUID REFERENCES public.wallets(id) ON DELETE CASCADE NOT NULL,
    stripe_card_id TEXT NOT NULL UNIQUE,
    stripe_cardholder_id TEXT NOT NULL,
    type card_type NOT NULL DEFAULT 'virtual',
    status card_status NOT NULL DEFAULT 'active',
    last_four TEXT NOT NULL,
    brand TEXT NOT NULL,
    spending_limit DECIMAL(12,2) NOT NULL DEFAULT 0,
    spent_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    expires_month INTEGER NOT NULL,
    expires_year INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT positive_spending_limit CHECK (spending_limit >= 0),
    CONSTRAINT positive_spent_amount CHECK (spent_amount >= 0),
    CONSTRAINT valid_expiry_month CHECK (expires_month >= 1 AND expires_month <= 12),
    CONSTRAINT valid_expiry_year CHECK (expires_year >= EXTRACT(YEAR FROM NOW()))
);

-- Transactions table
CREATE TABLE public.transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    wallet_id UUID REFERENCES public.wallets(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    card_id UUID REFERENCES public.cards(id) ON DELETE SET NULL,
    type transaction_type NOT NULL,
    status transaction_status NOT NULL DEFAULT 'pending',
    amount DECIMAL(12,2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    description TEXT,
    merchant_name TEXT,
    merchant_category TEXT,
    stripe_payment_intent_id TEXT,
    stripe_transaction_id TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT positive_amount CHECK (amount > 0)
);

-- Messages table (for group chat)
CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    wallet_id UUID REFERENCES public.wallets(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text',
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    type notification_type NOT NULL,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    data JSONB,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_stripe_customer_id ON public.users(stripe_customer_id);

CREATE INDEX idx_wallets_created_by ON public.wallets(created_by);
CREATE INDEX idx_wallets_status ON public.wallets(status);
CREATE INDEX idx_wallets_type ON public.wallets(type);

CREATE INDEX idx_wallet_members_user_id ON public.wallet_members(user_id);
CREATE INDEX idx_wallet_members_wallet_id ON public.wallet_members(wallet_id);
CREATE INDEX idx_wallet_members_role ON public.wallet_members(role);

CREATE INDEX idx_invitations_code ON public.invitations(code);
CREATE INDEX idx_invitations_email ON public.invitations(email);
CREATE INDEX idx_invitations_status ON public.invitations(status);
CREATE INDEX idx_invitations_wallet_id ON public.invitations(wallet_id);

CREATE INDEX idx_cards_user_id ON public.cards(user_id);
CREATE INDEX idx_cards_wallet_id ON public.cards(wallet_id);
CREATE INDEX idx_cards_stripe_card_id ON public.cards(stripe_card_id);
CREATE INDEX idx_cards_status ON public.cards(status);

CREATE INDEX idx_transactions_wallet_id ON public.transactions(wallet_id);
CREATE INDEX idx_transactions_user_id ON public.transactions(user_id);
CREATE INDEX idx_transactions_card_id ON public.transactions(card_id);
CREATE INDEX idx_transactions_type ON public.transactions(type);
CREATE INDEX idx_transactions_status ON public.transactions(status);
CREATE INDEX idx_transactions_created_at ON public.transactions(created_at DESC);

CREATE INDEX idx_messages_wallet_id ON public.messages(wallet_id);
CREATE INDEX idx_messages_user_id ON public.messages(user_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at DESC);

CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_type ON public.notifications(type);
CREATE INDEX idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX idx_notifications_created_at ON public.notifications(created_at DESC);
