{"roots": ["potto_app"], "packages": [{"name": "potto_app", "version": "1.0.0+1", "dependencies": ["cached_network_image", "dio", "equatable", "firebase_core", "firebase_messaging", "flutter", "flutter_bloc", "flutter_dotenv", "flutter_launcher_icons", "flutter_local_notifications", "flutter_native_splash", "flutter_svg", "go_router", "intl", "json_annotation", "permission_handler", "retrofit", "share_plus", "shared_preferences", "shimmer", "stripe_android", "stripe_ios", "stripe_platform_interface", "supabase_flutter", "url_launcher", "uuid"], "devDependencies": ["bloc_test", "build_runner", "flutter_lints", "flutter_test", "integration_test", "json_serializable", "<PERSON><PERSON>", "retrofit_generator"]}, {"name": "integration_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "file", "flutter", "flutter_driver", "flutter_test", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "sync_http", "term_glyph", "test_api", "vector_math", "vm_service", "webdriver"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "flutter_launcher_icons", "version": "0.13.1", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "intl", "version": "0.19.0", "dependencies": ["clock", "meta", "path"]}, {"name": "shimmer", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "webdriver", "version": "3.1.0", "dependencies": ["matcher", "path", "stack_trace", "sync_http"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "sync_http", "version": "0.3.1", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "flutter_driver", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "file", "flutter", "flutter_test", "fuchsia_remote_debug_protocol", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "platform", "process", "source_span", "stack_trace", "stream_channel", "string_scanner", "sync_http", "term_glyph", "test_api", "vector_math", "vm_service", "webdriver"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "process", "version": "5.0.3", "dependencies": ["file", "path", "platform"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "fuchsia_remote_debug_protocol", "version": "0.0.0", "dependencies": ["file", "meta", "path", "platform", "process", "vm_service"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "share_plus", "version": "7.2.2", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "win32"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "go_router", "version": "12.1.3", "dependencies": ["collection", "flutter", "flutter_web_plugins", "logging", "meta"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "share_plus_platform_interface", "version": "3.4.0", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "bloc_test", "version": "9.1.7", "dependencies": ["bloc", "diff_match_patch", "meta", "mocktail", "test"]}, {"name": "diff_match_patch", "version": "0.4.1", "dependencies": []}, {"name": "flutter_lints", "version": "3.0.2", "dependencies": ["lints"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "flutter_dotenv", "version": "5.2.1", "dependencies": ["flutter"]}, {"name": "stripe_ios", "version": "10.2.0", "dependencies": ["flutter"]}, {"name": "equatable", "version": "2.0.7", "dependencies": ["collection", "meta"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "mime", "version": "1.0.6", "dependencies": []}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "firebase_messaging_web", "version": "3.5.18", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "js", "meta"]}, {"name": "firebase_messaging", "version": "14.7.10", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "stripe_platform_interface", "version": "10.2.0", "dependencies": ["flutter", "freezed_annotation", "json_annotation", "meta", "plugin_platform_interface"]}, {"name": "freezed_annotation", "version": "2.4.4", "dependencies": ["collection", "json_annotation", "meta"]}, {"name": "flutter_bloc", "version": "8.1.6", "dependencies": ["bloc", "flutter", "provider"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "bloc", "version": "8.1.4", "dependencies": ["meta"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "flutter_local_notifications", "version": "16.3.3", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "timezone"]}, {"name": "flutter_local_notifications_linux", "version": "4.0.1", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "flutter_local_notifications_platform_interface", "version": "7.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "stripe_android", "version": "10.2.1", "dependencies": ["flutter"]}, {"name": "source_gen", "version": "1.5.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "source_span", "yaml"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "json_serializable", "version": "6.9.0", "dependencies": ["analyzer", "async", "build", "build_config", "collection", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "source_helper", "version": "1.3.5", "dependencies": ["analyzer", "collection", "source_gen"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "<PERSON><PERSON>", "version": "5.4.5", "dependencies": ["analyzer", "build", "code_builder", "collection", "dart_style", "matcher", "meta", "path", "source_gen", "test_api"]}, {"name": "macros", "version": "0.1.3-main.0", "dependencies": ["_macros"]}, {"name": "_macros", "version": "0.3.3", "dependencies": []}, {"name": "dart_style", "version": "2.3.8", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span"]}, {"name": "analyzer", "version": "6.11.0", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "macros", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "76.0.0", "dependencies": ["meta"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "timezone", "version": "0.9.4", "dependencies": ["path"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_common", "version": "2.5.6", "dependencies": ["meta", "path", "synchronized"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "build", "version": "2.5.4", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path", "pool"]}, {"name": "build_runner", "version": "2.5.4", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "build_runner_core", "version": "9.1.2", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_resolvers", "version": "2.5.4", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform"]}, {"name": "built_value", "version": "8.11.0", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "retrofit_generator", "version": "8.2.1", "dependencies": ["analyzer", "build", "built_collection", "code_builder", "dart_style", "dio", "protobuf", "retrofit", "source_gen", "tuple"]}, {"name": "protobuf", "version": "3.1.0", "dependencies": ["collection", "fixnum", "meta"]}, {"name": "tuple", "version": "2.0.2", "dependencies": []}, {"name": "retrofit", "version": "4.7.0", "dependencies": ["dio", "meta"]}, {"name": "dio", "version": "5.8.0+1", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "path"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "url_launcher", "version": "6.3.2", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "5.4.2", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "mocktail", "version": "1.0.4", "dependencies": ["collection", "matcher", "test_api"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "flutter_svg", "version": "2.2.0", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics_compiler", "version": "1.1.17", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "vector_graphics", "version": "1.1.19", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "flutter_native_splash", "version": "2.4.6", "dependencies": ["ansicolor", "args", "flutter", "flutter_web_plugins", "html", "image", "meta", "path", "universal_io", "xml", "yaml"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "universal_io", "version": "2.2.2", "dependencies": ["collection", "meta", "typed_data"]}, {"name": "ansicolor", "version": "2.0.3", "dependencies": []}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "firebase_core", "version": "2.32.0", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "firebase_core_web", "version": "2.24.0", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "supabase_flutter", "version": "2.9.1", "dependencies": ["app_links", "async", "crypto", "flutter", "http", "logging", "meta", "path_provider", "shared_preferences", "supabase", "url_launcher", "web"]}, {"name": "supabase", "version": "2.8.0", "dependencies": ["functions_client", "gotrue", "http", "logging", "postgrest", "realtime_client", "rxdart", "storage_client", "yet_another_json_isolate"]}, {"name": "yet_another_json_isolate", "version": "2.1.0", "dependencies": ["async"]}, {"name": "storage_client", "version": "2.4.0", "dependencies": ["http", "http_parser", "logging", "meta", "mime", "retry"]}, {"name": "realtime_client", "version": "2.5.1", "dependencies": ["collection", "http", "logging", "meta", "web_socket_channel"]}, {"name": "postgrest", "version": "2.4.2", "dependencies": ["http", "logging", "meta", "yet_another_json_isolate"]}, {"name": "gotrue", "version": "2.13.0", "dependencies": ["collection", "crypto", "http", "jwt_decode", "logging", "meta", "retry", "rxdart", "web"]}, {"name": "functions_client", "version": "2.4.3", "dependencies": ["http", "logging", "yet_another_json_isolate"]}, {"name": "jwt_decode", "version": "0.3.1", "dependencies": []}, {"name": "retry", "version": "3.1.2", "dependencies": []}, {"name": "app_links", "version": "6.4.0", "dependencies": ["app_links_linux", "app_links_platform_interface", "app_links_web", "flutter"]}, {"name": "app_links_web", "version": "1.0.4", "dependencies": ["app_links_platform_interface", "flutter", "flutter_web_plugins", "web"]}, {"name": "app_links_platform_interface", "version": "2.0.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "app_links_linux", "version": "1.0.3", "dependencies": ["app_links_platform_interface", "flutter", "gtk"]}, {"name": "gtk", "version": "2.1.0", "dependencies": ["ffi", "flutter", "meta"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "firebase_messaging_platform_interface", "version": "4.5.37", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "_flutterfire_internals", "version": "1.3.35", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "test", "version": "1.25.15", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "test_core", "version": "0.6.8", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "coverage", "version": "1.15.0", "dependencies": ["args", "cli_config", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service", "yaml"]}, {"name": "cli_config", "version": "0.2.0", "dependencies": ["args", "yaml"]}], "configVersion": 1}