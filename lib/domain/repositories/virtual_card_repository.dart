import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/virtual_card.dart';

abstract class VirtualCardRepository {
  // Virtual Card Management
  Future<Either<Failure, VirtualCard>> createVirtualCard(CreateVirtualCardRequest request);
  Future<Either<Failure, VirtualCard>> getVirtualCard(String cardId);
  Future<Either<Failure, List<VirtualCard>>> getVirtualCardsByWallet(String walletId);
  Future<Either<Failure, List<VirtualCard>>> getVirtualCardsByUser(String userId);
  Future<Either<Failure, VirtualCard>> updateVirtualCard(UpdateVirtualCardRequest request);
  Future<Either<Failure, void>> deleteVirtualCard(String cardId);
  
  // Card Status Management
  Future<Either<Failure, VirtualCard>> activateCard(String cardId);
  Future<Either<Failure, VirtualCard>> deactivateCard(String cardId);
  Future<Either<Failure, VirtualCard>> cancelCard(String cardId);
  
  // Spending Controls
  Future<Either<Failure, SpendingControl>> createSpendingControl(CreateSpendingControlRequest request);
  Future<Either<Failure, List<SpendingControl>>> getSpendingControls(String cardId);
  Future<Either<Failure, SpendingControl>> updateSpendingControl(String controlId, CreateSpendingControlRequest request);
  Future<Either<Failure, void>> deleteSpendingControl(String controlId);
  
  // Card Details & Security
  Future<Either<Failure, Map<String, dynamic>>> getCardDetails(String cardId);
  Future<Either<Failure, String>> getCardPin(String cardId);
  Future<Either<Failure, void>> updateCardPin(String cardId, String newPin);
  
  // Transaction Authorization
  Future<Either<Failure, bool>> authorizeTransaction({
    required String cardId,
    required double amount,
    required String currency,
    required String merchantName,
    required String merchantCategory,
    Map<String, dynamic>? metadata,
  });
}
