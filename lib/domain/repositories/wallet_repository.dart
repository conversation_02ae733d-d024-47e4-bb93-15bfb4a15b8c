import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/wallet.dart';
import '../entities/invitation.dart';
import '../usecases/wallet/create_wallet_usecase.dart';
import '../usecases/wallet/invitation_usecase.dart';
import '../usecases/wallet/member_management_usecase.dart';

abstract class WalletRepository {
  // Wallet Management
  Future<Either<Failure, Wallet>> createWallet(CreateWalletRequest request);
  Future<Either<Failure, Wallet>> getWallet(String walletId);
  Future<Either<Failure, List<Wallet>>> getUserWallets();
  Future<Either<Failure, Wallet>> updateWallet(String walletId, CreateWalletRequest request);
  Future<Either<Failure, void>> deleteWallet(String walletId);

  // Wallet Joining
  Future<Either<Failure, Wallet>> joinWallet(String inviteCode);
  Future<Either<Failure, Wallet>> acceptInvitation(String inviteCode);

  // Member Management
  Future<Either<Failure, List<WalletMember>>> getWalletMembers(String walletId);
  Future<Either<Failure, WalletMember>> updateMemberRole(String walletId, String userId, MemberRole role);
  Future<Either<Failure, WalletMember>> updateMemberPermissions(String walletId, String userId, UpdateMemberPermissionsRequest request);
  Future<Either<Failure, void>> removeMember(String walletId, String userId);

  // Invitation Management
  Future<Either<Failure, Invitation>> createInvitation(CreateInvitationRequest request);
  Future<Either<Failure, List<Invitation>>> getWalletInvitations(String walletId);
  Future<Either<Failure, void>> cancelInvitation(String invitationId);

  // Wallet Settings
  Future<Either<Failure, WalletSettings>> updateWalletSettings(String walletId, WalletSettings settings);
}
