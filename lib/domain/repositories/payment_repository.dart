import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/payment.dart';

abstract class PaymentRepository {
  // Payment Intents
  Future<Either<Failure, PaymentIntent>> createPaymentIntent(CreatePaymentIntentRequest request);
  Future<Either<Failure, PaymentIntent>> confirmPaymentIntent(String paymentIntentId, String paymentMethodId);
  Future<Either<Failure, PaymentIntent>> getPaymentIntent(String paymentIntentId);
  
  // Customers
  Future<Either<Failure, StripeCustomer>> createCustomer(CreateCustomerRequest request);
  Future<Either<Failure, StripeCustomer>> getCustomer(String customerId);
  Future<Either<Failure, StripeCustomer>> updateCustomer(String customerId, CreateCustomerRequest request);
  
  // Payment Methods
  Future<Either<Failure, List<PaymentMethod>>> getCustomerPaymentMethods(String customerId);
  Future<Either<Failure, PaymentMethod>> attachPaymentMethodToCustomer(String paymentMethodId, String customerId);
  Future<Either<Failure, void>> detachPaymentMethodFromCustomer(String paymentMethodId);
  
  // Payment Sheet
  Future<Either<Failure, void>> initPaymentSheet({
    required String paymentIntentClientSecret,
    String? customerId,
    String? customerEphemeralKeySecret,
    String? merchantDisplayName,
  });
  Future<Either<Failure, void>> presentPaymentSheet();
  
  // Webhooks
  Future<Either<Failure, WebhookEvent>> processWebhookEvent(String payload, String signature);
}
