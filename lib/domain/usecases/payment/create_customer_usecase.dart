import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/payment.dart';
import '../../repositories/payment_repository.dart';

class CreateCustomerUseCase implements UseCase<StripeCustomer, CreateCustomerParams> {
  final PaymentRepository _repository;

  CreateCustomerUseCase(this._repository);

  @override
  Future<Either<Failure, StripeCustomer>> call(CreateCustomerParams params) async {
    return await _repository.createCustomer(params.request);
  }
}

class CreateCustomerParams {
  final CreateCustomerRequest request;

  CreateCustomerParams({required this.request});
}
