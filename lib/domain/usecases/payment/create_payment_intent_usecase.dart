import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/payment.dart';
import '../../repositories/payment_repository.dart';

class CreatePaymentIntentUseCase implements UseCase<PaymentIntent, CreatePaymentIntentParams> {
  final PaymentRepository _repository;

  CreatePaymentIntentUseCase(this._repository);

  @override
  Future<Either<Failure, PaymentIntent>> call(CreatePaymentIntentParams params) async {
    return await _repository.createPaymentIntent(params.request);
  }
}

class CreatePaymentIntentParams {
  final CreatePaymentIntentRequest request;

  CreatePaymentIntentParams({required this.request});
}
