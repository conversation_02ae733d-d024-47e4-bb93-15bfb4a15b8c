import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/payment.dart';
import '../../repositories/payment_repository.dart';

class ProcessPaymentUseCase implements UseCase<PaymentIntent, ProcessPaymentParams> {
  final PaymentRepository _repository;

  ProcessPaymentUseCase(this._repository);

  @override
  Future<Either<Failure, PaymentIntent>> call(ProcessPaymentParams params) async {
    // First, initialize the payment sheet
    final initResult = await _repository.initPaymentSheet(
      paymentIntentClientSecret: params.clientSecret,
      customerId: params.customerId,
      customerEphemeralKeySecret: params.customerEphemeralKeySecret,
      merchantDisplayName: params.merchantDisplayName,
    );

    if (initResult.isLeft()) {
      return Left(initResult.fold((l) => l, (r) => throw Exception()));
    }

    // Present the payment sheet
    final presentResult = await _repository.presentPaymentSheet();
    
    if (presentResult.isLeft()) {
      return Left(presentResult.fold((l) => l, (r) => throw Exception()));
    }

    // Get the updated payment intent
    return await _repository.getPaymentIntent(params.paymentIntentId);
  }
}

class ProcessPaymentParams {
  final String paymentIntentId;
  final String clientSecret;
  final String? customerId;
  final String? customerEphemeralKeySecret;
  final String? merchantDisplayName;

  ProcessPaymentParams({
    required this.paymentIntentId,
    required this.clientSecret,
    this.customerId,
    this.customerEphemeralKeySecret,
    this.merchantDisplayName,
  });
}
