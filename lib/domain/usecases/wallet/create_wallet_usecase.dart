import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/wallet.dart';
import '../../repositories/wallet_repository.dart';

class CreateWalletUseCase implements UseCase<Wallet, CreateWalletParams> {
  final WalletRepository repository;

  CreateWalletUseCase(this.repository);

  @override
  Future<Either<Failure, Wallet>> call(CreateWalletParams params) async {
    return await repository.createWallet(params.request);
  }
}

class CreateWalletParams {
  final CreateWalletRequest request;

  CreateWalletParams({required this.request});
}

class CreateWalletRequest {
  final String name;
  final String? description;
  final WalletType type;
  final double goalAmount;
  final String currency;
  final DateTime? deadline;
  final String? imageUrl;
  final WalletSettings? settings;

  CreateWalletRequest({
    required this.name,
    this.description,
    required this.type,
    required this.goalAmount,
    required this.currency,
    this.deadline,
    this.imageUrl,
    this.settings,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'type': type.name,
      'goal_amount': goalAmount,
      'currency': currency,
      'deadline': deadline?.toIso8601String(),
      'image_url': imageUrl,
    };
  }
}
