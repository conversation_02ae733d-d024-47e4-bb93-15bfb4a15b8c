import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/wallet.dart';
import '../../repositories/wallet_repository.dart';

class JoinWalletUseCase implements UseCase<Wallet, JoinWalletParams> {
  final WalletRepository repository;

  JoinWalletUseCase(this.repository);

  @override
  Future<Either<Failure, Wallet>> call(JoinWalletParams params) async {
    return await repository.joinWallet(params.inviteCode);
  }
}

class JoinWalletParams {
  final String inviteCode;

  JoinWalletParams({required this.inviteCode});
}
