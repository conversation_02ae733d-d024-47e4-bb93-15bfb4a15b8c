import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/wallet.dart';
import '../../repositories/wallet_repository.dart';

class GetWalletsUseCase implements UseCase<List<Wallet>, NoParams> {
  final WalletRepository repository;

  GetWalletsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Wallet>>> call(NoParams params) async {
    return await repository.getUserWallets();
  }
}

class GetWalletUseCase implements UseCase<Wallet, GetWalletParams> {
  final WalletRepository repository;

  GetWalletUseCase(this.repository);

  @override
  Future<Either<Failure, Wallet>> call(GetWalletParams params) async {
    return await repository.getWallet(params.walletId);
  }
}

class GetWalletParams {
  final String walletId;

  GetWalletParams({required this.walletId});
}
