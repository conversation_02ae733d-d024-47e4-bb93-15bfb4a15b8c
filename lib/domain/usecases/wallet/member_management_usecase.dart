import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/wallet.dart';
import '../../repositories/wallet_repository.dart';

class UpdateMemberRoleUseCase implements UseCase<WalletMember, UpdateMemberRoleParams> {
  final WalletRepository repository;

  UpdateMemberRoleUseCase(this.repository);

  @override
  Future<Either<Failure, WalletMember>> call(UpdateMemberRoleParams params) async {
    return await repository.updateMemberRole(
      params.walletId,
      params.userId,
      params.role,
    );
  }
}

class UpdateMemberRoleParams {
  final String walletId;
  final String userId;
  final MemberRole role;

  UpdateMemberRoleParams({
    required this.walletId,
    required this.userId,
    required this.role,
  });
}

class UpdateMemberPermissionsUseCase implements UseCase<WalletMember, UpdateMemberPermissionsParams> {
  final WalletRepository repository;

  UpdateMemberPermissionsUseCase(this.repository);

  @override
  Future<Either<Failure, WalletMember>> call(UpdateMemberPermissionsParams params) async {
    return await repository.updateMemberPermissions(
      params.walletId,
      params.userId,
      params.request,
    );
  }
}

class UpdateMemberPermissionsParams {
  final String walletId;
  final String userId;
  final UpdateMemberPermissionsRequest request;

  UpdateMemberPermissionsParams({
    required this.walletId,
    required this.userId,
    required this.request,
  });
}

class UpdateMemberPermissionsRequest {
  final double? spendingLimit;
  final bool? canSpend;
  final bool? canInvite;

  UpdateMemberPermissionsRequest({
    this.spendingLimit,
    this.canSpend,
    this.canInvite,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (spendingLimit != null) data['spending_limit'] = spendingLimit;
    if (canSpend != null) data['can_spend'] = canSpend;
    if (canInvite != null) data['can_invite'] = canInvite;
    return data;
  }
}

class RemoveMemberUseCase implements UseCase<void, RemoveMemberParams> {
  final WalletRepository repository;

  RemoveMemberUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(RemoveMemberParams params) async {
    return await repository.removeMember(params.walletId, params.userId);
  }
}

class RemoveMemberParams {
  final String walletId;
  final String userId;

  RemoveMemberParams({
    required this.walletId,
    required this.userId,
  });
}

class GetWalletMembersUseCase implements UseCase<List<WalletMember>, GetWalletMembersParams> {
  final WalletRepository repository;

  GetWalletMembersUseCase(this.repository);

  @override
  Future<Either<Failure, List<WalletMember>>> call(GetWalletMembersParams params) async {
    return await repository.getWalletMembers(params.walletId);
  }
}

class GetWalletMembersParams {
  final String walletId;

  GetWalletMembersParams({required this.walletId});
}
