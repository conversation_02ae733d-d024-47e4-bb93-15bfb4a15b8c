import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/virtual_card.dart';
import '../../repositories/virtual_card_repository.dart';

class GetVirtualCardsByWalletUseCase implements UseCase<List<VirtualCard>, GetVirtualCardsByWalletParams> {
  final VirtualCardRepository repository;

  GetVirtualCardsByWalletUseCase(this.repository);

  @override
  Future<Either<Failure, List<VirtualCard>>> call(GetVirtualCardsByWalletParams params) async {
    return await repository.getVirtualCardsByWallet(params.walletId);
  }
}

class GetVirtualCardsByWalletParams {
  final String walletId;

  GetVirtualCardsByWalletParams({required this.walletId});
}

class GetVirtualCardsByUserUseCase implements UseCase<List<VirtualCard>, GetVirtualCardsByUserParams> {
  final VirtualCardRepository repository;

  GetVirtualCardsByUserUseCase(this.repository);

  @override
  Future<Either<Failure, List<VirtualCard>>> call(GetVirtualCardsByUserParams params) async {
    return await repository.getVirtualCardsByUser(params.userId);
  }
}

class GetVirtualCardsByUserParams {
  final String userId;

  GetVirtualCardsByUserParams({required this.userId});
}

class GetVirtualCardUseCase implements UseCase<VirtualCard, GetVirtualCardParams> {
  final VirtualCardRepository repository;

  GetVirtualCardUseCase(this.repository);

  @override
  Future<Either<Failure, VirtualCard>> call(GetVirtualCardParams params) async {
    return await repository.getVirtualCard(params.cardId);
  }
}

class GetVirtualCardParams {
  final String cardId;

  GetVirtualCardParams({required this.cardId});
}
