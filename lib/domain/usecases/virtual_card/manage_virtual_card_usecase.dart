import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/virtual_card.dart';
import '../../repositories/virtual_card_repository.dart';

class UpdateVirtualCardUseCase implements UseCase<VirtualCard, UpdateVirtualCardParams> {
  final VirtualCardRepository repository;

  UpdateVirtualCardUseCase(this.repository);

  @override
  Future<Either<Failure, VirtualCard>> call(UpdateVirtualCardParams params) async {
    return await repository.updateVirtualCard(params.request);
  }
}

class UpdateVirtualCardParams {
  final UpdateVirtualCardRequest request;

  UpdateVirtualCardParams({required this.request});
}

class ActivateCardUseCase implements UseCase<VirtualCard, ActivateCardParams> {
  final VirtualCardRepository repository;

  ActivateCardUseCase(this.repository);

  @override
  Future<Either<Failure, VirtualCard>> call(ActivateCardParams params) async {
    return await repository.activateCard(params.cardId);
  }
}

class ActivateCardParams {
  final String cardId;

  ActivateCardParams({required this.cardId});
}

class DeactivateCardUseCase implements UseCase<VirtualCard, DeactivateCardParams> {
  final VirtualCardRepository repository;

  DeactivateCardUseCase(this.repository);

  @override
  Future<Either<Failure, VirtualCard>> call(DeactivateCardParams params) async {
    return await repository.deactivateCard(params.cardId);
  }
}

class DeactivateCardParams {
  final String cardId;

  DeactivateCardParams({required this.cardId});
}

class CancelCardUseCase implements UseCase<VirtualCard, CancelCardParams> {
  final VirtualCardRepository repository;

  CancelCardUseCase(this.repository);

  @override
  Future<Either<Failure, VirtualCard>> call(CancelCardParams params) async {
    return await repository.cancelCard(params.cardId);
  }
}

class CancelCardParams {
  final String cardId;

  CancelCardParams({required this.cardId});
}

class DeleteVirtualCardUseCase implements UseCase<void, DeleteVirtualCardParams> {
  final VirtualCardRepository repository;

  DeleteVirtualCardUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteVirtualCardParams params) async {
    return await repository.deleteVirtualCard(params.cardId);
  }
}

class DeleteVirtualCardParams {
  final String cardId;

  DeleteVirtualCardParams({required this.cardId});
}

class GetCardDetailsUseCase implements UseCase<Map<String, dynamic>, GetCardDetailsParams> {
  final VirtualCardRepository repository;

  GetCardDetailsUseCase(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetCardDetailsParams params) async {
    return await repository.getCardDetails(params.cardId);
  }
}

class GetCardDetailsParams {
  final String cardId;

  GetCardDetailsParams({required this.cardId});
}
