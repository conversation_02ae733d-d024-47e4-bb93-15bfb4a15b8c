import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/virtual_card.dart';
import '../../repositories/virtual_card_repository.dart';

class CreateVirtualCardUseCase implements UseCase<VirtualCard, CreateVirtualCardParams> {
  final VirtualCardRepository repository;

  CreateVirtualCardUseCase(this.repository);

  @override
  Future<Either<Failure, VirtualCard>> call(CreateVirtualCardParams params) async {
    return await repository.createVirtualCard(params.request);
  }
}

class CreateVirtualCardParams {
  final CreateVirtualCardRequest request;

  CreateVirtualCardParams({required this.request});
}
