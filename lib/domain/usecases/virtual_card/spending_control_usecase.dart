import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/virtual_card.dart';
import '../../repositories/virtual_card_repository.dart';

class CreateSpendingControlUseCase implements UseCase<SpendingControl, CreateSpendingControlParams> {
  final VirtualCardRepository repository;

  CreateSpendingControlUseCase(this.repository);

  @override
  Future<Either<Failure, SpendingControl>> call(CreateSpendingControlParams params) async {
    return await repository.createSpendingControl(params.request);
  }
}

class CreateSpendingControlParams {
  final CreateSpendingControlRequest request;

  CreateSpendingControlParams({required this.request});
}

class GetSpendingControlsUseCase implements UseCase<List<SpendingControl>, GetSpendingControlsParams> {
  final VirtualCardRepository repository;

  GetSpendingControlsUseCase(this.repository);

  @override
  Future<Either<Failure, List<SpendingControl>>> call(GetSpendingControlsParams params) async {
    return await repository.getSpendingControls(params.cardId);
  }
}

class GetSpendingControlsParams {
  final String cardId;

  GetSpendingControlsParams({required this.cardId});
}

class UpdateSpendingControlUseCase implements UseCase<SpendingControl, UpdateSpendingControlParams> {
  final VirtualCardRepository repository;

  UpdateSpendingControlUseCase(this.repository);

  @override
  Future<Either<Failure, SpendingControl>> call(UpdateSpendingControlParams params) async {
    return await repository.updateSpendingControl(params.controlId, params.request);
  }
}

class UpdateSpendingControlParams {
  final String controlId;
  final CreateSpendingControlRequest request;

  UpdateSpendingControlParams({
    required this.controlId,
    required this.request,
  });
}

class DeleteSpendingControlUseCase implements UseCase<void, DeleteSpendingControlParams> {
  final VirtualCardRepository repository;

  DeleteSpendingControlUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteSpendingControlParams params) async {
    return await repository.deleteSpendingControl(params.controlId);
  }
}

class DeleteSpendingControlParams {
  final String controlId;

  DeleteSpendingControlParams({required this.controlId});
}

class AuthorizeTransactionUseCase implements UseCase<bool, AuthorizeTransactionParams> {
  final VirtualCardRepository repository;

  AuthorizeTransactionUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(AuthorizeTransactionParams params) async {
    return await repository.authorizeTransaction(
      cardId: params.cardId,
      amount: params.amount,
      currency: params.currency,
      merchantName: params.merchantName,
      merchantCategory: params.merchantCategory,
      metadata: params.metadata,
    );
  }
}

class AuthorizeTransactionParams {
  final String cardId;
  final double amount;
  final String currency;
  final String merchantName;
  final String merchantCategory;
  final Map<String, dynamic>? metadata;

  AuthorizeTransactionParams({
    required this.cardId,
    required this.amount,
    required this.currency,
    required this.merchantName,
    required this.merchantCategory,
    this.metadata,
  });
}
