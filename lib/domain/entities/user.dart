import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String email;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final UserPreferences preferences;
  
  const User({
    required this.id,
    required this.email,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.avatarUrl,
    required this.createdAt,
    required this.updatedAt,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.preferences,
  });
  
  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    return firstName ?? lastName ?? email.split('@').first;
  }
  
  String get initials {
    if (firstName != null && lastName != null) {
      return '${firstName![0]}${lastName![0]}'.toUpperCase();
    }
    if (firstName != null) {
      return firstName![0].toUpperCase();
    }
    return email[0].toUpperCase();
  }
  
  User copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    UserPreferences? preferences,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      preferences: preferences ?? this.preferences,
    );
  }
  
  @override
  List<Object?> get props => [
        id,
        email,
        firstName,
        lastName,
        phoneNumber,
        avatarUrl,
        createdAt,
        updatedAt,
        isEmailVerified,
        isPhoneVerified,
        preferences,
      ];
}

class UserPreferences extends Equatable {
  final bool notificationsEnabled;
  final bool emailNotificationsEnabled;
  final bool pushNotificationsEnabled;
  final bool transactionNotificationsEnabled;
  final bool invitationNotificationsEnabled;
  final String currency;
  final String language;
  final String timezone;
  final bool biometricEnabled;
  final bool darkModeEnabled;
  
  const UserPreferences({
    required this.notificationsEnabled,
    required this.emailNotificationsEnabled,
    required this.pushNotificationsEnabled,
    required this.transactionNotificationsEnabled,
    required this.invitationNotificationsEnabled,
    required this.currency,
    required this.language,
    required this.timezone,
    required this.biometricEnabled,
    required this.darkModeEnabled,
  });
  
  UserPreferences copyWith({
    bool? notificationsEnabled,
    bool? emailNotificationsEnabled,
    bool? pushNotificationsEnabled,
    bool? transactionNotificationsEnabled,
    bool? invitationNotificationsEnabled,
    String? currency,
    String? language,
    String? timezone,
    bool? biometricEnabled,
    bool? darkModeEnabled,
  }) {
    return UserPreferences(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      emailNotificationsEnabled: emailNotificationsEnabled ?? this.emailNotificationsEnabled,
      pushNotificationsEnabled: pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      transactionNotificationsEnabled: transactionNotificationsEnabled ?? this.transactionNotificationsEnabled,
      invitationNotificationsEnabled: invitationNotificationsEnabled ?? this.invitationNotificationsEnabled,
      currency: currency ?? this.currency,
      language: language ?? this.language,
      timezone: timezone ?? this.timezone,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      darkModeEnabled: darkModeEnabled ?? this.darkModeEnabled,
    );
  }
  
  @override
  List<Object?> get props => [
        notificationsEnabled,
        emailNotificationsEnabled,
        pushNotificationsEnabled,
        transactionNotificationsEnabled,
        invitationNotificationsEnabled,
        currency,
        language,
        timezone,
        biometricEnabled,
        darkModeEnabled,
      ];
}
