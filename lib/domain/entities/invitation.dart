import 'package:equatable/equatable.dart';

enum InvitationStatus { pending, accepted, expired, cancelled }

class Invitation extends Equatable {
  final String id;
  final String walletId;
  final String invitedBy;
  final String email;
  final String code;
  final InvitationStatus status;
  final DateTime expiresAt;
  final DateTime? acceptedAt;
  final String? acceptedBy;
  final DateTime createdAt;

  const Invitation({
    required this.id,
    required this.walletId,
    required this.invitedBy,
    required this.email,
    required this.code,
    required this.status,
    required this.expiresAt,
    this.acceptedAt,
    this.acceptedBy,
    required this.createdAt,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isPending => status == InvitationStatus.pending && !isExpired;
  bool get canBeAccepted => isPending;

  Invitation copyWith({
    String? id,
    String? walletId,
    String? invitedBy,
    String? email,
    String? code,
    InvitationStatus? status,
    DateTime? expiresAt,
    DateTime? acceptedAt,
    String? acceptedBy,
    DateTime? createdAt,
  }) {
    return Invitation(
      id: id ?? this.id,
      walletId: walletId ?? this.walletId,
      invitedBy: invitedBy ?? this.invitedBy,
      email: email ?? this.email,
      code: code ?? this.code,
      status: status ?? this.status,
      expiresAt: expiresAt ?? this.expiresAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      acceptedBy: acceptedBy ?? this.acceptedBy,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        walletId,
        invitedBy,
        email,
        code,
        status,
        expiresAt,
        acceptedAt,
        acceptedBy,
        createdAt,
      ];
}
