import 'package:equatable/equatable.dart';

enum VirtualCardStatus {
  active,
  inactive,
  canceled,
  pending,
}

enum VirtualCardType {
  virtual,
  physical,
}

enum SpendingControlType {
  amount,
  interval,
  category,
  merchant,
}

enum SpendingInterval {
  perTransaction,
  daily,
  weekly,
  monthly,
  yearly,
  allTime,
}

class VirtualCard extends Equatable {
  final String id;
  final String walletId;
  final String userId;
  final String cardholderName;
  final String last4;
  final String brand;
  final int expMonth;
  final int expYear;
  final VirtualCardStatus status;
  final VirtualCardType type;
  final String? stripeCardId;
  final String? stripeCardholderId;
  final List<SpendingControl> spendingControls;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const VirtualCard({
    required this.id,
    required this.walletId,
    required this.userId,
    required this.cardholderName,
    required this.last4,
    required this.brand,
    required this.expMonth,
    required this.expYear,
    required this.status,
    required this.type,
    this.stripeCardId,
    this.stripeCardholderId,
    this.spendingControls = const [],
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  String get maskedNumber => '**** **** **** $last4';

  String get expiryDate => '${expMonth.toString().padLeft(2, '0')}/${expYear.toString().substring(2)}';

  bool get isActive => status == VirtualCardStatus.active;
  bool get isExpired {
    final now = DateTime.now();
    final expiry = DateTime(expYear, expMonth + 1, 0);
    return now.isAfter(expiry);
  }

  double get totalSpendingLimit {
    final amountControls = spendingControls
        .where((control) => control.type == SpendingControlType.amount)
        .toList();
    
    if (amountControls.isEmpty) return double.infinity;
    
    return amountControls
        .map((control) => control.amount ?? 0.0)
        .reduce((a, b) => a < b ? a : b);
  }

  VirtualCard copyWith({
    String? id,
    String? walletId,
    String? userId,
    String? cardholderName,
    String? last4,
    String? brand,
    int? expMonth,
    int? expYear,
    VirtualCardStatus? status,
    VirtualCardType? type,
    String? stripeCardId,
    String? stripeCardholderId,
    List<SpendingControl>? spendingControls,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VirtualCard(
      id: id ?? this.id,
      walletId: walletId ?? this.walletId,
      userId: userId ?? this.userId,
      cardholderName: cardholderName ?? this.cardholderName,
      last4: last4 ?? this.last4,
      brand: brand ?? this.brand,
      expMonth: expMonth ?? this.expMonth,
      expYear: expYear ?? this.expYear,
      status: status ?? this.status,
      type: type ?? this.type,
      stripeCardId: stripeCardId ?? this.stripeCardId,
      stripeCardholderId: stripeCardholderId ?? this.stripeCardholderId,
      spendingControls: spendingControls ?? this.spendingControls,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        walletId,
        userId,
        cardholderName,
        last4,
        brand,
        expMonth,
        expYear,
        status,
        type,
        stripeCardId,
        stripeCardholderId,
        spendingControls,
        metadata,
        createdAt,
        updatedAt,
      ];
}

class SpendingControl extends Equatable {
  final String id;
  final String cardId;
  final SpendingControlType type;
  final double? amount;
  final SpendingInterval? interval;
  final List<String>? categories;
  final List<String>? allowedMerchants;
  final List<String>? blockedMerchants;
  final bool isActive;
  final DateTime? validFrom;
  final DateTime? validUntil;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SpendingControl({
    required this.id,
    required this.cardId,
    required this.type,
    this.amount,
    this.interval,
    this.categories,
    this.allowedMerchants,
    this.blockedMerchants,
    this.isActive = true,
    this.validFrom,
    this.validUntil,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isCurrentlyValid {
    final now = DateTime.now();
    if (validFrom != null && now.isBefore(validFrom!)) return false;
    if (validUntil != null && now.isAfter(validUntil!)) return false;
    return isActive;
  }

  SpendingControl copyWith({
    String? id,
    String? cardId,
    SpendingControlType? type,
    double? amount,
    SpendingInterval? interval,
    List<String>? categories,
    List<String>? allowedMerchants,
    List<String>? blockedMerchants,
    bool? isActive,
    DateTime? validFrom,
    DateTime? validUntil,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SpendingControl(
      id: id ?? this.id,
      cardId: cardId ?? this.cardId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      interval: interval ?? this.interval,
      categories: categories ?? this.categories,
      allowedMerchants: allowedMerchants ?? this.allowedMerchants,
      blockedMerchants: blockedMerchants ?? this.blockedMerchants,
      isActive: isActive ?? this.isActive,
      validFrom: validFrom ?? this.validFrom,
      validUntil: validUntil ?? this.validUntil,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        cardId,
        type,
        amount,
        interval,
        categories,
        allowedMerchants,
        blockedMerchants,
        isActive,
        validFrom,
        validUntil,
        createdAt,
        updatedAt,
      ];
}

// Request classes for virtual card operations
class CreateVirtualCardRequest extends Equatable {
  final String walletId;
  final String userId;
  final String cardholderName;
  final VirtualCardType type;
  final List<SpendingControl> spendingControls;
  final Map<String, dynamic>? metadata;

  const CreateVirtualCardRequest({
    required this.walletId,
    required this.userId,
    required this.cardholderName,
    this.type = VirtualCardType.virtual,
    this.spendingControls = const [],
    this.metadata,
  });

  @override
  List<Object?> get props => [
        walletId,
        userId,
        cardholderName,
        type,
        spendingControls,
        metadata,
      ];
}

class UpdateVirtualCardRequest extends Equatable {
  final String cardId;
  final VirtualCardStatus? status;
  final List<SpendingControl>? spendingControls;
  final Map<String, dynamic>? metadata;

  const UpdateVirtualCardRequest({
    required this.cardId,
    this.status,
    this.spendingControls,
    this.metadata,
  });

  @override
  List<Object?> get props => [cardId, status, spendingControls, metadata];
}

class CreateSpendingControlRequest extends Equatable {
  final String cardId;
  final SpendingControlType type;
  final double? amount;
  final SpendingInterval? interval;
  final List<String>? categories;
  final List<String>? allowedMerchants;
  final List<String>? blockedMerchants;
  final DateTime? validFrom;
  final DateTime? validUntil;

  const CreateSpendingControlRequest({
    required this.cardId,
    required this.type,
    this.amount,
    this.interval,
    this.categories,
    this.allowedMerchants,
    this.blockedMerchants,
    this.validFrom,
    this.validUntil,
  });

  @override
  List<Object?> get props => [
        cardId,
        type,
        amount,
        interval,
        categories,
        allowedMerchants,
        blockedMerchants,
        validFrom,
        validUntil,
      ];
}
