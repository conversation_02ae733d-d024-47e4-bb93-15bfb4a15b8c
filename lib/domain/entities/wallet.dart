import 'package:equatable/equatable.dart';

enum WalletStatus { active, locked, closed }

enum WalletType { travel, event, gift, general }

class Wallet extends Equatable {
  final String id;
  final String name;
  final String? description;
  final WalletType type;
  final WalletStatus status;
  final double goalAmount;
  final double currentAmount;
  final String currency;
  final DateTime? deadline;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? imageUrl;
  final List<WalletMember> members;
  final WalletSettings settings;
  
  const Wallet({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.status,
    required this.goalAmount,
    required this.currentAmount,
    required this.currency,
    this.deadline,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.imageUrl,
    required this.members,
    required this.settings,
  });
  
  double get progressPercentage {
    if (goalAmount <= 0) return 0.0;
    return (currentAmount / goalAmount).clamp(0.0, 1.0);
  }
  
  double get remainingAmount {
    return (goalAmount - currentAmount).clamp(0.0, double.infinity);
  }
  
  bool get isGoalReached => currentAmount >= goalAmount;
  
  bool get isDeadlinePassed {
    if (deadline == null) return false;
    return DateTime.now().isAfter(deadline!);
  }
  
  bool get canSpend => status == WalletStatus.active && currentAmount > 0;
  
  bool get canContribute => status == WalletStatus.active && !isDeadlinePassed;
  
  int get memberCount => members.length;
  
  WalletMember? getMember(String userId) {
    try {
      return members.firstWhere((member) => member.userId == userId);
    } catch (e) {
      return null;
    }
  }
  
  bool isAdmin(String userId) {
    final member = getMember(userId);
    return member?.role == MemberRole.admin;
  }
  
  bool isMember(String userId) {
    return getMember(userId) != null;
  }
  
  Wallet copyWith({
    String? id,
    String? name,
    String? description,
    WalletType? type,
    WalletStatus? status,
    double? goalAmount,
    double? currentAmount,
    String? currency,
    DateTime? deadline,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? imageUrl,
    List<WalletMember>? members,
    WalletSettings? settings,
  }) {
    return Wallet(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      goalAmount: goalAmount ?? this.goalAmount,
      currentAmount: currentAmount ?? this.currentAmount,
      currency: currency ?? this.currency,
      deadline: deadline ?? this.deadline,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      imageUrl: imageUrl ?? this.imageUrl,
      members: members ?? this.members,
      settings: settings ?? this.settings,
    );
  }
  
  @override
  List<Object?> get props => [
        id,
        name,
        description,
        type,
        status,
        goalAmount,
        currentAmount,
        currency,
        deadline,
        createdBy,
        createdAt,
        updatedAt,
        imageUrl,
        members,
        settings,
      ];
}

enum MemberRole { admin, member, viewer }

class WalletMember extends Equatable {
  final String id;
  final String userId;
  final String walletId;
  final MemberRole role;
  final double contributedAmount;
  final double spendingLimit;
  final bool canSpend;
  final bool canInvite;
  final DateTime joinedAt;
  final String? invitedBy;
  
  const WalletMember({
    required this.id,
    required this.userId,
    required this.walletId,
    required this.role,
    required this.contributedAmount,
    required this.spendingLimit,
    required this.canSpend,
    required this.canInvite,
    required this.joinedAt,
    this.invitedBy,
  });
  
  bool get isAdmin => role == MemberRole.admin;
  bool get canManageWallet => role == MemberRole.admin;
  bool get hasSpendingLimit => spendingLimit > 0;
  
  WalletMember copyWith({
    String? id,
    String? userId,
    String? walletId,
    MemberRole? role,
    double? contributedAmount,
    double? spendingLimit,
    bool? canSpend,
    bool? canInvite,
    DateTime? joinedAt,
    String? invitedBy,
  }) {
    return WalletMember(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      walletId: walletId ?? this.walletId,
      role: role ?? this.role,
      contributedAmount: contributedAmount ?? this.contributedAmount,
      spendingLimit: spendingLimit ?? this.spendingLimit,
      canSpend: canSpend ?? this.canSpend,
      canInvite: canInvite ?? this.canInvite,
      joinedAt: joinedAt ?? this.joinedAt,
      invitedBy: invitedBy ?? this.invitedBy,
    );
  }
  
  @override
  List<Object?> get props => [
        id,
        userId,
        walletId,
        role,
        contributedAmount,
        spendingLimit,
        canSpend,
        canInvite,
        joinedAt,
        invitedBy,
      ];
}

class WalletSettings extends Equatable {
  final bool requireApprovalForSpending;
  final double approvalThreshold;
  final bool allowMemberInvites;
  final bool allowMemberSpending;
  final bool notifyOnTransactions;
  final bool notifyOnContributions;
  final bool notifyOnInvitations;
  final bool autoLockOnGoalReached;
  final bool autoLockOnDeadline;
  
  const WalletSettings({
    required this.requireApprovalForSpending,
    required this.approvalThreshold,
    required this.allowMemberInvites,
    required this.allowMemberSpending,
    required this.notifyOnTransactions,
    required this.notifyOnContributions,
    required this.notifyOnInvitations,
    required this.autoLockOnGoalReached,
    required this.autoLockOnDeadline,
  });
  
  WalletSettings copyWith({
    bool? requireApprovalForSpending,
    double? approvalThreshold,
    bool? allowMemberInvites,
    bool? allowMemberSpending,
    bool? notifyOnTransactions,
    bool? notifyOnContributions,
    bool? notifyOnInvitations,
    bool? autoLockOnGoalReached,
    bool? autoLockOnDeadline,
  }) {
    return WalletSettings(
      requireApprovalForSpending: requireApprovalForSpending ?? this.requireApprovalForSpending,
      approvalThreshold: approvalThreshold ?? this.approvalThreshold,
      allowMemberInvites: allowMemberInvites ?? this.allowMemberInvites,
      allowMemberSpending: allowMemberSpending ?? this.allowMemberSpending,
      notifyOnTransactions: notifyOnTransactions ?? this.notifyOnTransactions,
      notifyOnContributions: notifyOnContributions ?? this.notifyOnContributions,
      notifyOnInvitations: notifyOnInvitations ?? this.notifyOnInvitations,
      autoLockOnGoalReached: autoLockOnGoalReached ?? this.autoLockOnGoalReached,
      autoLockOnDeadline: autoLockOnDeadline ?? this.autoLockOnDeadline,
    );
  }
  
  @override
  List<Object?> get props => [
        requireApprovalForSpending,
        approvalThreshold,
        allowMemberInvites,
        allowMemberSpending,
        notifyOnTransactions,
        notifyOnContributions,
        notifyOnInvitations,
        autoLockOnGoalReached,
        autoLockOnDeadline,
      ];
}
