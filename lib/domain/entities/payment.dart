import 'package:equatable/equatable.dart';

enum PaymentIntentStatus {
  requiresPaymentMethod,
  requiresConfirmation,
  requiresAction,
  processing,
  requiresCapture,
  canceled,
  succeeded,
}

enum PaymentMethodType {
  card,
  bankAccount,
  applePay,
  googlePay,
}

enum CardBrand {
  visa,
  mastercard,
  amex,
  discover,
  jcb,
  dinersClub,
  unionPay,
  unknown,
}

enum CardFunding {
  credit,
  debit,
  prepaid,
  unknown,
}

class PaymentIntent extends Equatable {
  final String id;
  final int amount; // Amount in cents
  final String currency;
  final PaymentIntentStatus status;
  final String clientSecret;
  final String? description;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;

  const PaymentIntent({
    required this.id,
    required this.amount,
    required this.currency,
    required this.status,
    required this.clientSecret,
    this.description,
    this.metadata,
    required this.createdAt,
  });

  double get amountInDollars => amount / 100.0;

  bool get isSucceeded => status == PaymentIntentStatus.succeeded;
  bool get requiresAction => status == PaymentIntentStatus.requiresAction;
  bool get isProcessing => status == PaymentIntentStatus.processing;
  bool get isCanceled => status == PaymentIntentStatus.canceled;

  @override
  List<Object?> get props => [
        id,
        amount,
        currency,
        status,
        clientSecret,
        description,
        metadata,
        createdAt,
      ];
}

class PaymentMethod extends Equatable {
  final String id;
  final PaymentMethodType type;
  final CardDetails? card;
  final String? customerId;
  final DateTime createdAt;

  const PaymentMethod({
    required this.id,
    required this.type,
    this.card,
    this.customerId,
    required this.createdAt,
  });

  bool get isCard => type == PaymentMethodType.card;

  @override
  List<Object?> get props => [id, type, card, customerId, createdAt];
}

class CardDetails extends Equatable {
  final CardBrand brand;
  final String last4;
  final int expMonth;
  final int expYear;
  final CardFunding? funding;
  final String? country;

  const CardDetails({
    required this.brand,
    required this.last4,
    required this.expMonth,
    required this.expYear,
    this.funding,
    this.country,
  });

  String get displayBrand {
    switch (brand) {
      case CardBrand.visa:
        return 'Visa';
      case CardBrand.mastercard:
        return 'Mastercard';
      case CardBrand.amex:
        return 'American Express';
      case CardBrand.discover:
        return 'Discover';
      case CardBrand.jcb:
        return 'JCB';
      case CardBrand.dinersClub:
        return 'Diners Club';
      case CardBrand.unionPay:
        return 'UnionPay';
      case CardBrand.unknown:
        return 'Unknown';
    }
  }

  String get maskedNumber => '**** **** **** $last4';

  String get expiryDate => '${expMonth.toString().padLeft(2, '0')}/${expYear.toString().substring(2)}';

  bool get isExpired {
    final now = DateTime.now();
    final expiry = DateTime(expYear, expMonth + 1, 0); // Last day of expiry month
    return now.isAfter(expiry);
  }

  @override
  List<Object?> get props => [brand, last4, expMonth, expYear, funding, country];
}

class StripeCustomer extends Equatable {
  final String id;
  final String email;
  final String? name;
  final String? phone;
  final String? description;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;

  const StripeCustomer({
    required this.id,
    required this.email,
    this.name,
    this.phone,
    this.description,
    this.metadata,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [id, email, name, phone, description, metadata, createdAt];
}

class WebhookEvent extends Equatable {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final bool livemode;

  const WebhookEvent({
    required this.id,
    required this.type,
    required this.data,
    required this.createdAt,
    this.livemode = false,
  });

  bool get isPaymentIntentEvent => type.startsWith('payment_intent.');
  bool get isPaymentMethodEvent => type.startsWith('payment_method.');
  bool get isCustomerEvent => type.startsWith('customer.');
  bool get isCardEvent => type.startsWith('issuing_card.');

  @override
  List<Object?> get props => [id, type, data, createdAt, livemode];
}

// Payment request classes
class CreatePaymentIntentRequest extends Equatable {
  final int amount;
  final String currency;
  final String? customerId;
  final String? paymentMethodId;
  final String? description;
  final Map<String, dynamic>? metadata;
  final bool confirmImmediately;

  const CreatePaymentIntentRequest({
    required this.amount,
    required this.currency,
    this.customerId,
    this.paymentMethodId,
    this.description,
    this.metadata,
    this.confirmImmediately = false,
  });

  @override
  List<Object?> get props => [
        amount,
        currency,
        customerId,
        paymentMethodId,
        description,
        metadata,
        confirmImmediately,
      ];
}

class CreateCustomerRequest extends Equatable {
  final String email;
  final String? name;
  final String? phone;
  final String? description;
  final Map<String, dynamic>? metadata;

  const CreateCustomerRequest({
    required this.email,
    this.name,
    this.phone,
    this.description,
    this.metadata,
  });

  @override
  List<Object?> get props => [email, name, phone, description, metadata];
}
