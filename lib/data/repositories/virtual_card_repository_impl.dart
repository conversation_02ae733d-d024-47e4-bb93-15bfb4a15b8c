import 'package:dartz/dartz.dart';
import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../domain/entities/virtual_card.dart';
import '../../domain/repositories/virtual_card_repository.dart';
import '../datasources/virtual_card_remote_datasource.dart';

class VirtualCardRepositoryImpl implements VirtualCardRepository {
  final VirtualCardRemoteDataSource remoteDataSource;

  VirtualCardRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, VirtualCard>> createVirtualCard(CreateVirtualCardRequest request) async {
    try {
      final result = await remoteDataSource.createVirtualCard(request);
      return Right(result.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, VirtualCard>> getVirtualCard(String cardId) async {
    try {
      final result = await remoteDataSource.getVirtualCard(cardId);
      return Right(result.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<VirtualCard>>> getVirtualCardsByWallet(String walletId) async {
    try {
      final result = await remoteDataSource.getVirtualCardsByWallet(walletId);
      return Right(result.map((model) => model.toEntity()).toList());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<VirtualCard>>> getVirtualCardsByUser(String userId) async {
    try {
      final result = await remoteDataSource.getVirtualCardsByUser(userId);
      return Right(result.map((model) => model.toEntity()).toList());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, VirtualCard>> updateVirtualCard(UpdateVirtualCardRequest request) async {
    try {
      final result = await remoteDataSource.updateVirtualCard(request);
      return Right(result.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteVirtualCard(String cardId) async {
    try {
      await remoteDataSource.deleteVirtualCard(cardId);
      return const Right(null);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, VirtualCard>> activateCard(String cardId) async {
    try {
      final result = await remoteDataSource.activateCard(cardId);
      return Right(result.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, VirtualCard>> deactivateCard(String cardId) async {
    try {
      final result = await remoteDataSource.deactivateCard(cardId);
      return Right(result.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, VirtualCard>> cancelCard(String cardId) async {
    try {
      final result = await remoteDataSource.cancelCard(cardId);
      return Right(result.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, SpendingControl>> createSpendingControl(CreateSpendingControlRequest request) async {
    try {
      final result = await remoteDataSource.createSpendingControl(request);
      return Right(result.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<SpendingControl>>> getSpendingControls(String cardId) async {
    try {
      final result = await remoteDataSource.getSpendingControls(cardId);
      return Right(result.map((model) => model.toEntity()).toList());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, SpendingControl>> updateSpendingControl(String controlId, CreateSpendingControlRequest request) async {
    try {
      final result = await remoteDataSource.updateSpendingControl(controlId, request);
      return Right(result.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteSpendingControl(String controlId) async {
    try {
      await remoteDataSource.deleteSpendingControl(controlId);
      return const Right(null);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getCardDetails(String cardId) async {
    try {
      final result = await remoteDataSource.getCardDetails(cardId);
      return Right(result);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> getCardPin(String cardId) async {
    try {
      final result = await remoteDataSource.getCardPin(cardId);
      return Right(result);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateCardPin(String cardId, String newPin) async {
    try {
      await remoteDataSource.updateCardPin(cardId, newPin);
      return const Right(null);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> authorizeTransaction({
    required String cardId,
    required double amount,
    required String currency,
    required String merchantName,
    required String merchantCategory,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final result = await remoteDataSource.authorizeTransaction(
        cardId: cardId,
        amount: amount,
        currency: currency,
        merchantName: merchantName,
        merchantCategory: merchantCategory,
        metadata: metadata,
      );
      return Right(result);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(message: e.message, code: e.code));
    } on CardException catch (e) {
      return Left(CardFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }
}
