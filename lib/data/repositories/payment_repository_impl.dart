import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/errors/exceptions.dart';
import '../../domain/entities/payment.dart';
import '../../domain/repositories/payment_repository.dart';
import '../datasources/payment_remote_datasource.dart';

class PaymentRepositoryImpl implements PaymentRepository {
  final PaymentRemoteDataSource _remoteDataSource;

  PaymentRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, PaymentIntent>> createPaymentIntent(CreatePaymentIntentRequest request) async {
    try {
      final result = await _remoteDataSource.createPaymentIntent(request);
      return Right(result);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, PaymentIntent>> confirmPaymentIntent(String paymentIntentId, String paymentMethodId) async {
    try {
      final result = await _remoteDataSource.confirmPaymentIntent(paymentIntentId, paymentMethodId);
      return Right(result);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, PaymentIntent>> getPaymentIntent(String paymentIntentId) async {
    try {
      final result = await _remoteDataSource.getPaymentIntent(paymentIntentId);
      return Right(result);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, StripeCustomer>> createCustomer(CreateCustomerRequest request) async {
    try {
      final result = await _remoteDataSource.createCustomer(request);
      return Right(result);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, StripeCustomer>> getCustomer(String customerId) async {
    try {
      final result = await _remoteDataSource.getCustomer(customerId);
      return Right(result);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, StripeCustomer>> updateCustomer(String customerId, CreateCustomerRequest request) async {
    try {
      final result = await _remoteDataSource.updateCustomer(customerId, request);
      return Right(result);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, List<PaymentMethod>>> getCustomerPaymentMethods(String customerId) async {
    try {
      final result = await _remoteDataSource.getCustomerPaymentMethods(customerId);
      return Right(result);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, PaymentMethod>> attachPaymentMethodToCustomer(String paymentMethodId, String customerId) async {
    try {
      final result = await _remoteDataSource.attachPaymentMethodToCustomer(paymentMethodId, customerId);
      return Right(result);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, void>> detachPaymentMethodFromCustomer(String paymentMethodId) async {
    try {
      await _remoteDataSource.detachPaymentMethodFromCustomer(paymentMethodId);
      return const Right(null);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, void>> initPaymentSheet({
    required String paymentIntentClientSecret,
    String? customerId,
    String? customerEphemeralKeySecret,
    String? merchantDisplayName,
  }) async {
    try {
      await _remoteDataSource.initPaymentSheet(
        paymentIntentClientSecret: paymentIntentClientSecret,
        customerId: customerId,
        customerEphemeralKeySecret: customerEphemeralKeySecret,
        merchantDisplayName: merchantDisplayName,
      );
      return const Right(null);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(PaymentFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, void>> presentPaymentSheet() async {
    try {
      await _remoteDataSource.presentPaymentSheet();
      return const Right(null);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(PaymentFailure(message: e.toString(), code: 500));
    }
  }

  @override
  Future<Either<Failure, WebhookEvent>> processWebhookEvent(String payload, String signature) async {
    try {
      final result = await _remoteDataSource.processWebhookEvent(payload, signature);
      return Right(result);
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(ServerFailure(message: e.toString(), code: 500));
    }
  }
}
