import '../../domain/entities/wallet.dart';

class WalletModel {
  final String id;
  final String name;
  final String? description;
  final String type;
  final String status;
  final double goalAmount;
  final double currentAmount;
  final String currency;
  final DateTime? deadline;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? imageUrl;
  final List<WalletMemberModel> members;
  final WalletSettingsModel settings;

  WalletModel({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.status,
    required this.goalAmount,
    required this.currentAmount,
    required this.currency,
    this.deadline,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.imageUrl,
    required this.members,
    required this.settings,
  });

  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: json['type'],
      status: json['status'],
      goalAmount: (json['goal_amount'] as num).toDouble(),
      currentAmount: (json['current_amount'] as num).toDouble(),
      currency: json['currency'],
      deadline:
          json['deadline'] != null ? DateTime.parse(json['deadline']) : null,
      createdBy: json['created_by'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      imageUrl: json['image_url'],
      members:
          (json['wallet_members'] as List<dynamic>?)
              ?.map((memberJson) => WalletMemberModel.fromJson(memberJson))
              .toList() ??
          [],
      settings:
          json['wallet_settings'] != null
              ? WalletSettingsModel.fromJson(json['wallet_settings'])
              : WalletSettingsModel.defaultSettings(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type,
      'status': status,
      'goal_amount': goalAmount,
      'current_amount': currentAmount,
      'currency': currency,
      'deadline': deadline?.toIso8601String(),
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'image_url': imageUrl,
    };
  }

  Wallet toEntity() {
    return Wallet(
      id: id,
      name: name,
      description: description,
      type: WalletType.values.firstWhere((e) => e.name == type),
      status: WalletStatus.values.firstWhere((e) => e.name == status),
      goalAmount: goalAmount,
      currentAmount: currentAmount,
      currency: currency,
      deadline: deadline,
      createdBy: createdBy,
      createdAt: createdAt,
      updatedAt: updatedAt,
      imageUrl: imageUrl,
      members: members.map((member) => member.toEntity()).toList(),
      settings: settings.toEntity(),
    );
  }
}

class WalletMemberModel {
  final String id;
  final String userId;
  final String walletId;
  final String role;
  final double contributedAmount;
  final double spendingLimit;
  final bool canSpend;
  final bool canInvite;
  final DateTime joinedAt;
  final String? invitedBy;

  WalletMemberModel({
    required this.id,
    required this.userId,
    required this.walletId,
    required this.role,
    required this.contributedAmount,
    required this.spendingLimit,
    required this.canSpend,
    required this.canInvite,
    required this.joinedAt,
    this.invitedBy,
  });

  factory WalletMemberModel.fromJson(Map<String, dynamic> json) {
    return WalletMemberModel(
      id: json['id'],
      userId: json['user_id'],
      walletId: json['wallet_id'],
      role: json['role'],
      contributedAmount: (json['contributed_amount'] as num).toDouble(),
      spendingLimit: (json['spending_limit'] as num).toDouble(),
      canSpend: json['can_spend'],
      canInvite: json['can_invite'],
      joinedAt: DateTime.parse(json['joined_at']),
      invitedBy: json['invited_by'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'wallet_id': walletId,
      'role': role,
      'contributed_amount': contributedAmount,
      'spending_limit': spendingLimit,
      'can_spend': canSpend,
      'can_invite': canInvite,
      'joined_at': joinedAt.toIso8601String(),
      'invited_by': invitedBy,
    };
  }

  WalletMember toEntity() {
    return WalletMember(
      id: id,
      userId: userId,
      walletId: walletId,
      role: MemberRole.values.firstWhere((e) => e.name == role),
      contributedAmount: contributedAmount,
      spendingLimit: spendingLimit,
      canSpend: canSpend,
      canInvite: canInvite,
      joinedAt: joinedAt,
      invitedBy: invitedBy,
    );
  }
}

class WalletSettingsModel {
  final bool requireApprovalForSpending;
  final double approvalThreshold;
  final bool allowMemberInvites;
  final bool allowMemberSpending;
  final bool notifyOnTransactions;
  final bool notifyOnContributions;
  final bool notifyOnInvitations;
  final bool autoLockOnGoalReached;
  final bool autoLockOnDeadline;

  WalletSettingsModel({
    required this.requireApprovalForSpending,
    required this.approvalThreshold,
    required this.allowMemberInvites,
    required this.allowMemberSpending,
    required this.notifyOnTransactions,
    required this.notifyOnContributions,
    required this.notifyOnInvitations,
    required this.autoLockOnGoalReached,
    required this.autoLockOnDeadline,
  });

  factory WalletSettingsModel.fromJson(Map<String, dynamic> json) {
    return WalletSettingsModel(
      requireApprovalForSpending:
          json['require_approval_for_spending'] ?? false,
      approvalThreshold:
          (json['approval_threshold'] as num?)?.toDouble() ?? 0.0,
      allowMemberInvites: json['allow_member_invites'] ?? true,
      allowMemberSpending: json['allow_member_spending'] ?? true,
      notifyOnTransactions: json['notify_on_transactions'] ?? true,
      notifyOnContributions: json['notify_on_contributions'] ?? true,
      notifyOnInvitations: json['notify_on_invitations'] ?? true,
      autoLockOnGoalReached: json['auto_lock_on_goal_reached'] ?? false,
      autoLockOnDeadline: json['auto_lock_on_deadline'] ?? false,
    );
  }

  factory WalletSettingsModel.defaultSettings() {
    return WalletSettingsModel(
      requireApprovalForSpending: false,
      approvalThreshold: 0.0,
      allowMemberInvites: true,
      allowMemberSpending: true,
      notifyOnTransactions: true,
      notifyOnContributions: true,
      notifyOnInvitations: true,
      autoLockOnGoalReached: false,
      autoLockOnDeadline: false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'require_approval_for_spending': requireApprovalForSpending,
      'approval_threshold': approvalThreshold,
      'allow_member_invites': allowMemberInvites,
      'allow_member_spending': allowMemberSpending,
      'notify_on_transactions': notifyOnTransactions,
      'notify_on_contributions': notifyOnContributions,
      'notify_on_invitations': notifyOnInvitations,
      'auto_lock_on_goal_reached': autoLockOnGoalReached,
      'auto_lock_on_deadline': autoLockOnDeadline,
    };
  }

  WalletSettings toEntity() {
    return WalletSettings(
      requireApprovalForSpending: requireApprovalForSpending,
      approvalThreshold: approvalThreshold,
      allowMemberInvites: allowMemberInvites,
      allowMemberSpending: allowMemberSpending,
      notifyOnTransactions: notifyOnTransactions,
      notifyOnContributions: notifyOnContributions,
      notifyOnInvitations: notifyOnInvitations,
      autoLockOnGoalReached: autoLockOnGoalReached,
      autoLockOnDeadline: autoLockOnDeadline,
    );
  }
}
