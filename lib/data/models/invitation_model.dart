import '../../domain/entities/invitation.dart';

class InvitationModel {
  final String id;
  final String walletId;
  final String invitedBy;
  final String email;
  final String code;
  final String status;
  final DateTime expiresAt;
  final DateTime? acceptedAt;
  final String? acceptedBy;
  final DateTime createdAt;

  InvitationModel({
    required this.id,
    required this.walletId,
    required this.invitedBy,
    required this.email,
    required this.code,
    required this.status,
    required this.expiresAt,
    this.acceptedAt,
    this.acceptedBy,
    required this.createdAt,
  });

  factory InvitationModel.fromJson(Map<String, dynamic> json) {
    return InvitationModel(
      id: json['id'],
      walletId: json['wallet_id'],
      invitedBy: json['invited_by'],
      email: json['email'],
      code: json['code'],
      status: json['status'],
      expiresAt: DateTime.parse(json['expires_at']),
      acceptedAt: json['accepted_at'] != null ? DateTime.parse(json['accepted_at']) : null,
      acceptedBy: json['accepted_by'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'wallet_id': walletId,
      'invited_by': invitedBy,
      'email': email,
      'code': code,
      'status': status,
      'expires_at': expiresAt.toIso8601String(),
      'accepted_at': acceptedAt?.toIso8601String(),
      'accepted_by': acceptedBy,
      'created_at': createdAt.toIso8601String(),
    };
  }

  Invitation toEntity() {
    return Invitation(
      id: id,
      walletId: walletId,
      invitedBy: invitedBy,
      email: email,
      code: code,
      status: InvitationStatus.values.firstWhere((e) => e.name == status),
      expiresAt: expiresAt,
      acceptedAt: acceptedAt,
      acceptedBy: acceptedBy,
      createdAt: createdAt,
    );
  }
}
