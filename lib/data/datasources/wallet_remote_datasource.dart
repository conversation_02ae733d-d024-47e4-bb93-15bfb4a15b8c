import '../models/wallet_model.dart';
import '../models/invitation_model.dart';
import '../../domain/entities/wallet.dart';
import '../../domain/usecases/wallet/create_wallet_usecase.dart';
import '../../domain/usecases/wallet/invitation_usecase.dart';
import '../../domain/usecases/wallet/member_management_usecase.dart';

abstract class WalletRemoteDataSource {
  // Wallet Management
  Future<WalletModel> createWallet(CreateWalletRequest request);
  Future<WalletModel> getWallet(String walletId);
  Future<List<WalletModel>> getUserWallets();
  Future<WalletModel> updateWallet(String walletId, CreateWalletRequest request);
  Future<void> deleteWallet(String walletId);

  // Wallet Joining
  Future<WalletModel> joinWallet(String inviteCode);
  Future<WalletModel> acceptInvitation(String inviteCode);

  // Member Management
  Future<List<WalletMemberModel>> getWalletMembers(String walletId);
  Future<WalletMemberModel> updateMemberRole(String walletId, String userId, MemberRole role);
  Future<WalletMemberModel> updateMemberPermissions(String walletId, String userId, UpdateMemberPermissionsRequest request);
  Future<void> removeMember(String walletId, String userId);

  // Invitation Management
  Future<InvitationModel> createInvitation(CreateInvitationRequest request);
  Future<List<InvitationModel>> getWalletInvitations(String walletId);
  Future<void> cancelInvitation(String invitationId);

  // Wallet Settings
  Future<WalletSettingsModel> updateWalletSettings(String walletId, WalletSettings settings);
}
