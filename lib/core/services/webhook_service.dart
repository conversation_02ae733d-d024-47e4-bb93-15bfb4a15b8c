import 'package:supabase_flutter/supabase_flutter.dart';
import '../errors/exceptions.dart';
import '../../domain/entities/payment.dart';
import '../../data/models/payment_model.dart';

class WebhookService {
  final SupabaseClient _supabaseClient;

  WebhookService(this._supabaseClient);

  Future<void> handleStripeWebhook(WebhookEvent event) async {
    try {
      switch (event.type) {
        case 'payment_intent.succeeded':
          await _handlePaymentIntentSucceeded(event);
          break;
        case 'payment_intent.payment_failed':
          await _handlePaymentIntentFailed(event);
          break;
        case 'payment_intent.canceled':
          await _handlePaymentIntentCanceled(event);
          break;
        case 'customer.created':
          await _handleCustomerCreated(event);
          break;
        case 'customer.updated':
          await _handleCustomerUpdated(event);
          break;
        case 'payment_method.attached':
          await _handlePaymentMethodAttached(event);
          break;
        case 'issuing_card.created':
          await _handleIssuingCardCreated(event);
          break;
        case 'issuing_authorization.created':
          await _handleIssuingAuthorizationCreated(event);
          break;
        default:
          print('Unhandled webhook event type: ${event.type}');
      }
    } catch (e) {
      throw ServerException(
        message: 'Failed to handle webhook event: ${e.toString()}',
        code: 500,
      );
    }
  }

  Future<void> _handlePaymentIntentSucceeded(WebhookEvent event) async {
    final paymentIntentData = event.data['object'] as Map<String, dynamic>;
    final paymentIntentId = paymentIntentData['id'] as String;
    final amount = paymentIntentData['amount'] as int;
    final currency = paymentIntentData['currency'] as String;
    final metadata = paymentIntentData['metadata'] as Map<String, dynamic>?;

    // Extract wallet and user information from metadata
    final walletId = metadata?['wallet_id'] as String?;
    final userId = metadata?['user_id'] as String?;
    final transactionType = metadata?['transaction_type'] as String? ?? 'contribution';

    if (walletId == null || userId == null) {
      throw ServerException(
        message: 'Missing wallet_id or user_id in payment intent metadata',
        code: 400,
      );
    }

    // Create transaction record
    await _supabaseClient.from('transactions').insert({
      'wallet_id': walletId,
      'user_id': userId,
      'type': transactionType,
      'status': 'completed',
      'amount': amount / 100.0, // Convert from cents to dollars
      'currency': currency,
      'description': 'Wallet contribution',
      'stripe_payment_intent_id': paymentIntentId,
      'metadata': metadata,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });

    // Update wallet member contribution if it's a contribution
    if (transactionType == 'contribution') {
      await _supabaseClient.rpc('update_member_contribution', params: {
        'p_wallet_id': walletId,
        'p_user_id': userId,
        'p_amount': amount / 100.0,
      });
    }

    // Send notification
    await _sendNotification(
      userId: userId,
      type: 'transaction',
      title: 'Payment Successful',
      body: 'Your payment of \$${(amount / 100.0).toStringAsFixed(2)} was processed successfully.',
      data: {
        'wallet_id': walletId,
        'transaction_type': transactionType,
        'amount': amount / 100.0,
      },
    );
  }

  Future<void> _handlePaymentIntentFailed(WebhookEvent event) async {
    final paymentIntentData = event.data['object'] as Map<String, dynamic>;
    final paymentIntentId = paymentIntentData['id'] as String;
    final amount = paymentIntentData['amount'] as int;
    final currency = paymentIntentData['currency'] as String;
    final metadata = paymentIntentData['metadata'] as Map<String, dynamic>?;

    final walletId = metadata?['wallet_id'] as String?;
    final userId = metadata?['user_id'] as String?;
    final transactionType = metadata?['transaction_type'] as String? ?? 'contribution';

    if (walletId == null || userId == null) return;

    // Create failed transaction record
    await _supabaseClient.from('transactions').insert({
      'wallet_id': walletId,
      'user_id': userId,
      'type': transactionType,
      'status': 'failed',
      'amount': amount / 100.0,
      'currency': currency,
      'description': 'Failed wallet contribution',
      'stripe_payment_intent_id': paymentIntentId,
      'metadata': metadata,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });

    // Send notification
    await _sendNotification(
      userId: userId,
      type: 'transaction',
      title: 'Payment Failed',
      body: 'Your payment of \$${(amount / 100.0).toStringAsFixed(2)} could not be processed.',
      data: {
        'wallet_id': walletId,
        'transaction_type': transactionType,
        'amount': amount / 100.0,
      },
    );
  }

  Future<void> _handlePaymentIntentCanceled(WebhookEvent event) async {
    final paymentIntentData = event.data['object'] as Map<String, dynamic>;
    final paymentIntentId = paymentIntentData['id'] as String;
    final metadata = paymentIntentData['metadata'] as Map<String, dynamic>?;

    final walletId = metadata?['wallet_id'] as String?;
    final userId = metadata?['user_id'] as String?;

    if (walletId == null || userId == null) return;

    // Update any existing transaction records
    await _supabaseClient
        .from('transactions')
        .update({
          'status': 'cancelled',
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('stripe_payment_intent_id', paymentIntentId);
  }

  Future<void> _handleCustomerCreated(WebhookEvent event) async {
    final customerData = event.data['object'] as Map<String, dynamic>;
    final customerId = customerData['id'] as String;
    final email = customerData['email'] as String;
    final metadata = customerData['metadata'] as Map<String, dynamic>?;

    final userId = metadata?['user_id'] as String?;
    if (userId == null) return;

    // Update user with Stripe customer ID
    await _supabaseClient
        .from('users')
        .update({
          'stripe_customer_id': customerId,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', userId);
  }

  Future<void> _handleCustomerUpdated(WebhookEvent event) async {
    // Handle customer updates if needed
    print('Customer updated: ${event.data}');
  }

  Future<void> _handlePaymentMethodAttached(WebhookEvent event) async {
    final paymentMethodData = event.data['object'] as Map<String, dynamic>;
    final customerId = paymentMethodData['customer'] as String?;
    
    if (customerId == null) return;

    // Find user by customer ID and send notification
    final userResponse = await _supabaseClient
        .from('users')
        .select('id')
        .eq('stripe_customer_id', customerId)
        .maybeSingle();

    if (userResponse != null) {
      await _sendNotification(
        userId: userResponse['id'],
        type: 'card',
        title: 'Payment Method Added',
        body: 'A new payment method has been added to your account.',
        data: {'customer_id': customerId},
      );
    }
  }

  Future<void> _handleIssuingCardCreated(WebhookEvent event) async {
    final cardData = event.data['object'] as Map<String, dynamic>;
    final cardId = cardData['id'] as String;
    final cardholderId = cardData['cardholder'] as String;
    final metadata = cardData['metadata'] as Map<String, dynamic>?;

    final walletId = metadata?['wallet_id'] as String?;
    final userId = metadata?['user_id'] as String?;

    if (walletId == null || userId == null) return;

    // Update card record in database
    await _supabaseClient
        .from('cards')
        .update({
          'stripe_card_id': cardId,
          'stripe_cardholder_id': cardholderId,
          'status': 'active',
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('wallet_id', walletId)
        .eq('user_id', userId);

    // Send notification
    await _sendNotification(
      userId: userId,
      type: 'card',
      title: 'Virtual Card Created',
      body: 'Your virtual card has been created and is ready to use.',
      data: {
        'wallet_id': walletId,
        'card_id': cardId,
      },
    );
  }

  Future<void> _handleIssuingAuthorizationCreated(WebhookEvent event) async {
    final authData = event.data['object'] as Map<String, dynamic>;
    final cardId = authData['card'] as String;
    final amount = authData['amount'] as int;
    final currency = authData['currency'] as String;
    final merchantData = authData['merchant_data'] as Map<String, dynamic>?;

    // Find card and wallet information
    final cardResponse = await _supabaseClient
        .from('cards')
        .select('user_id, wallet_id')
        .eq('stripe_card_id', cardId)
        .maybeSingle();

    if (cardResponse == null) return;

    final userId = cardResponse['user_id'];
    final walletId = cardResponse['wallet_id'];

    // Create spending transaction
    await _supabaseClient.from('transactions').insert({
      'wallet_id': walletId,
      'user_id': userId,
      'type': 'spending',
      'status': 'completed',
      'amount': amount / 100.0,
      'currency': currency,
      'description': 'Card spending',
      'merchant_name': merchantData?['name'],
      'merchant_category': merchantData?['category'],
      'stripe_transaction_id': authData['id'],
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });

    // Send notification
    await _sendNotification(
      userId: userId,
      type: 'transaction',
      title: 'Card Transaction',
      body: 'You spent \$${(amount / 100.0).toStringAsFixed(2)} at ${merchantData?['name'] ?? 'a merchant'}.',
      data: {
        'wallet_id': walletId,
        'amount': amount / 100.0,
        'merchant': merchantData?['name'],
      },
    );
  }

  Future<void> _sendNotification({
    required String userId,
    required String type,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    await _supabaseClient.from('notifications').insert({
      'user_id': userId,
      'type': type,
      'title': title,
      'body': body,
      'data': data,
      'is_read': false,
      'created_at': DateTime.now().toIso8601String(),
    });
  }
}
