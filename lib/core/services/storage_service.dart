import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  final SharedPreferences _prefs;
  
  StorageService(this._prefs);
  
  // String operations
  Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }
  
  String? getString(String key) {
    return _prefs.getString(key);
  }
  
  // Int operations
  Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }
  
  int? getInt(String key) {
    return _prefs.getInt(key);
  }
  
  // Bool operations
  Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }
  
  bool? getBool(String key) {
    return _prefs.getBool(key);
  }
  
  // Double operations
  Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }
  
  double? getDouble(String key) {
    return _prefs.getDouble(key);
  }
  
  // List operations
  Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }
  
  List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }
  
  // JSON operations
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }
  
  Map<String, dynamic>? getJson(String key) {
    final jsonString = getString(key);
    if (jsonString == null) return null;
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }
  
  // Remove operations
  Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }
  
  Future<bool> clear() async {
    return await _prefs.clear();
  }
  
  // Check if key exists
  bool containsKey(String key) {
    return _prefs.containsKey(key);
  }
  
  // Get all keys
  Set<String> getKeys() {
    return _prefs.getKeys();
  }
  
  // Convenience methods for common app data
  Future<bool> setUserToken(String token) async {
    return await setString('user_token', token);
  }
  
  String? getUserToken() {
    return getString('user_token');
  }
  
  Future<bool> setUserId(String userId) async {
    return await setString('user_id', userId);
  }
  
  String? getUserId() {
    return getString('user_id');
  }
  
  Future<bool> setThemeMode(String themeMode) async {
    return await setString('theme_mode', themeMode);
  }
  
  String? getThemeMode() {
    return getString('theme_mode');
  }
  
  Future<bool> setOnboardingCompleted(bool completed) async {
    return await setBool('onboarding_completed', completed);
  }
  
  bool getOnboardingCompleted() {
    return getBool('onboarding_completed') ?? false;
  }
  
  Future<bool> setBiometricEnabled(bool enabled) async {
    return await setBool('biometric_enabled', enabled);
  }
  
  bool getBiometricEnabled() {
    return getBool('biometric_enabled') ?? false;
  }
  
  Future<bool> setNotificationsEnabled(bool enabled) async {
    return await setBool('notifications_enabled', enabled);
  }
  
  bool getNotificationsEnabled() {
    return getBool('notifications_enabled') ?? true;
  }
  
  // Clear user data on logout
  Future<bool> clearUserData() async {
    final keys = [
      'user_token',
      'user_id',
      'biometric_enabled',
    ];
    
    bool success = true;
    for (final key in keys) {
      final result = await remove(key);
      if (!result) success = false;
    }
    
    return success;
  }
}
