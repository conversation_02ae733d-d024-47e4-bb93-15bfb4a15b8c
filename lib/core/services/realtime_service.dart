import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service for managing real-time subscriptions and events
class RealtimeService {
  final SupabaseClient _supabaseClient;
  final Map<String, RealtimeChannel> _channels = {};
  final Map<String, StreamController> _controllers = {};

  RealtimeService(this._supabaseClient);

  /// Subscribe to wallet updates for a specific wallet
  Stream<WalletRealtimeEvent> subscribeToWallet(String walletId) {
    final channelName = 'wallet_$walletId';
    
    if (_controllers.containsKey(channelName)) {
      return _controllers[channelName]!.stream as Stream<WalletRealtimeEvent>;
    }

    final controller = StreamController<WalletRealtimeEvent>.broadcast();
    _controllers[channelName] = controller;

    final channel = _supabaseClient.channel(channelName);
    
    // Subscribe to wallet table changes
    channel.onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'wallets',
      filter: PostgresChangeFilter(
        type: PostgresChangeFilterType.eq,
        column: 'id',
        value: walletId,
      ),
      callback: (payload) {
        _handleWalletChange(payload, controller);
      },
    );

    // Subscribe to wallet members changes
    channel.onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'wallet_members',
      filter: PostgresChangeFilter(
        type: PostgresChangeFilterType.eq,
        column: 'wallet_id',
        value: walletId,
      ),
      callback: (payload) {
        _handleWalletMemberChange(payload, controller);
      },
    );

    // Subscribe to transactions for this wallet
    channel.onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'transactions',
      filter: PostgresChangeFilter(
        type: PostgresChangeFilterType.eq,
        column: 'wallet_id',
        value: walletId,
      ),
      callback: (payload) {
        _handleTransactionChange(payload, controller);
      },
    );

    // Subscribe to invitations for this wallet
    channel.onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'invitations',
      filter: PostgresChangeFilter(
        type: PostgresChangeFilterType.eq,
        column: 'wallet_id',
        value: walletId,
      ),
      callback: (payload) {
        _handleInvitationChange(payload, controller);
      },
    );

    // Subscribe to custom wallet events
    channel.onBroadcast(
      event: 'wallet_updates',
      callback: (payload) {
        _handleCustomWalletEvent(payload, controller);
      },
    );

    channel.subscribe();
    _channels[channelName] = channel;

    return controller.stream;
  }

  /// Subscribe to user notifications
  Stream<NotificationRealtimeEvent> subscribeToNotifications(String userId) {
    final channelName = 'notifications_$userId';
    
    if (_controllers.containsKey(channelName)) {
      return _controllers[channelName]!.stream as Stream<NotificationRealtimeEvent>;
    }

    final controller = StreamController<NotificationRealtimeEvent>.broadcast();
    _controllers[channelName] = controller;

    final channel = _supabaseClient.channel(channelName);
    
    channel.onPostgresChanges(
      event: PostgresChangeEvent.insert,
      schema: 'public',
      table: 'notifications',
      filter: PostgresChangeFilter(
        type: PostgresChangeFilterType.eq,
        column: 'user_id',
        value: userId,
      ),
      callback: (payload) {
        _handleNotificationChange(payload, controller);
      },
    );

    channel.subscribe();
    _channels[channelName] = channel;

    return controller.stream;
  }

  /// Subscribe to card updates for a specific user
  Stream<CardRealtimeEvent> subscribeToCards(String userId) {
    final channelName = 'cards_$userId';
    
    if (_controllers.containsKey(channelName)) {
      return _controllers[channelName]!.stream as Stream<CardRealtimeEvent>;
    }

    final controller = StreamController<CardRealtimeEvent>.broadcast();
    _controllers[channelName] = controller;

    final channel = _supabaseClient.channel(channelName);
    
    channel.onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'cards',
      filter: PostgresChangeFilter(
        type: PostgresChangeFilterType.eq,
        column: 'user_id',
        value: userId,
      ),
      callback: (payload) {
        _handleCardChange(payload, controller);
      },
    );

    channel.subscribe();
    _channels[channelName] = channel;

    return controller.stream;
  }

  /// Subscribe to chat messages for a wallet
  Stream<MessageRealtimeEvent> subscribeToChat(String walletId) {
    final channelName = 'chat_$walletId';
    
    if (_controllers.containsKey(channelName)) {
      return _controllers[channelName]!.stream as Stream<MessageRealtimeEvent>;
    }

    final controller = StreamController<MessageRealtimeEvent>.broadcast();
    _controllers[channelName] = controller;

    final channel = _supabaseClient.channel(channelName);
    
    channel.onPostgresChanges(
      event: PostgresChangeEvent.insert,
      schema: 'public',
      table: 'messages',
      filter: PostgresChangeFilter(
        type: PostgresChangeFilterType.eq,
        column: 'wallet_id',
        value: walletId,
      ),
      callback: (payload) {
        _handleMessageChange(payload, controller);
      },
    );

    channel.subscribe();
    _channels[channelName] = channel;

    return controller.stream;
  }

  /// Unsubscribe from a specific channel
  void unsubscribe(String channelName) {
    final channel = _channels[channelName];
    if (channel != null) {
      channel.unsubscribe();
      _channels.remove(channelName);
    }

    final controller = _controllers[channelName];
    if (controller != null) {
      controller.close();
      _controllers.remove(channelName);
    }
  }

  /// Unsubscribe from all channels
  void unsubscribeAll() {
    for (final channel in _channels.values) {
      channel.unsubscribe();
    }
    _channels.clear();

    for (final controller in _controllers.values) {
      controller.close();
    }
    _controllers.clear();
  }

  void _handleWalletChange(
    PostgresChangePayload payload,
    StreamController<WalletRealtimeEvent> controller,
  ) {
    try {
      final event = WalletRealtimeEvent(
        type: _mapEventType(payload.eventType),
        walletId: payload.newRecord?['id'] ?? payload.oldRecord?['id'],
        data: payload.newRecord ?? payload.oldRecord ?? {},
        timestamp: DateTime.now(),
      );
      controller.add(event);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling wallet change: $e');
      }
    }
  }

  void _handleWalletMemberChange(
    PostgresChangePayload payload,
    StreamController<WalletRealtimeEvent> controller,
  ) {
    try {
      final event = WalletRealtimeEvent(
        type: RealtimeEventType.memberUpdate,
        walletId: payload.newRecord?['wallet_id'] ?? payload.oldRecord?['wallet_id'],
        data: payload.newRecord ?? payload.oldRecord ?? {},
        timestamp: DateTime.now(),
      );
      controller.add(event);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling wallet member change: $e');
      }
    }
  }

  void _handleTransactionChange(
    PostgresChangePayload payload,
    StreamController<WalletRealtimeEvent> controller,
  ) {
    try {
      final event = WalletRealtimeEvent(
        type: RealtimeEventType.transaction,
        walletId: payload.newRecord?['wallet_id'] ?? payload.oldRecord?['wallet_id'],
        data: payload.newRecord ?? payload.oldRecord ?? {},
        timestamp: DateTime.now(),
      );
      controller.add(event);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling transaction change: $e');
      }
    }
  }

  void _handleInvitationChange(
    PostgresChangePayload payload,
    StreamController<WalletRealtimeEvent> controller,
  ) {
    try {
      final event = WalletRealtimeEvent(
        type: RealtimeEventType.invitation,
        walletId: payload.newRecord?['wallet_id'] ?? payload.oldRecord?['wallet_id'],
        data: payload.newRecord ?? payload.oldRecord ?? {},
        timestamp: DateTime.now(),
      );
      controller.add(event);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling invitation change: $e');
      }
    }
  }

  void _handleCustomWalletEvent(
    Map<String, dynamic> payload,
    StreamController<WalletRealtimeEvent> controller,
  ) {
    try {
      final event = WalletRealtimeEvent(
        type: RealtimeEventType.custom,
        walletId: payload['wallet_id'],
        data: payload,
        timestamp: DateTime.now(),
      );
      controller.add(event);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling custom wallet event: $e');
      }
    }
  }

  void _handleNotificationChange(
    PostgresChangePayload payload,
    StreamController<NotificationRealtimeEvent> controller,
  ) {
    try {
      final event = NotificationRealtimeEvent(
        type: _mapEventType(payload.eventType),
        data: payload.newRecord ?? {},
        timestamp: DateTime.now(),
      );
      controller.add(event);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling notification change: $e');
      }
    }
  }

  void _handleCardChange(
    PostgresChangePayload payload,
    StreamController<CardRealtimeEvent> controller,
  ) {
    try {
      final event = CardRealtimeEvent(
        type: _mapEventType(payload.eventType),
        cardId: payload.newRecord?['id'] ?? payload.oldRecord?['id'],
        data: payload.newRecord ?? payload.oldRecord ?? {},
        timestamp: DateTime.now(),
      );
      controller.add(event);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling card change: $e');
      }
    }
  }

  void _handleMessageChange(
    PostgresChangePayload payload,
    StreamController<MessageRealtimeEvent> controller,
  ) {
    try {
      final event = MessageRealtimeEvent(
        type: _mapEventType(payload.eventType),
        walletId: payload.newRecord?['wallet_id'],
        data: payload.newRecord ?? {},
        timestamp: DateTime.now(),
      );
      controller.add(event);
    } catch (e) {
      if (kDebugMode) {
        print('Error handling message change: $e');
      }
    }
  }

  RealtimeEventType _mapEventType(PostgresChangeEvent eventType) {
    switch (eventType) {
      case PostgresChangeEvent.insert:
        return RealtimeEventType.insert;
      case PostgresChangeEvent.update:
        return RealtimeEventType.update;
      case PostgresChangeEvent.delete:
        return RealtimeEventType.delete;
      default:
        return RealtimeEventType.update;
    }
  }
}

/// Enum for realtime event types
enum RealtimeEventType {
  insert,
  update,
  delete,
  transaction,
  memberUpdate,
  invitation,
  custom,
}

/// Base class for realtime events
abstract class RealtimeEvent {
  final RealtimeEventType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  RealtimeEvent({
    required this.type,
    required this.data,
    required this.timestamp,
  });
}

/// Wallet-specific realtime event
class WalletRealtimeEvent extends RealtimeEvent {
  final String walletId;

  WalletRealtimeEvent({
    required super.type,
    required this.walletId,
    required super.data,
    required super.timestamp,
  });
}

/// Notification realtime event
class NotificationRealtimeEvent extends RealtimeEvent {
  NotificationRealtimeEvent({
    required super.type,
    required super.data,
    required super.timestamp,
  });
}

/// Card realtime event
class CardRealtimeEvent extends RealtimeEvent {
  final String cardId;

  CardRealtimeEvent({
    required super.type,
    required this.cardId,
    required super.data,
    required super.timestamp,
  });
}

/// Message realtime event
class MessageRealtimeEvent extends RealtimeEvent {
  final String walletId;

  MessageRealtimeEvent({
    required super.type,
    required this.walletId,
    required super.data,
    required super.timestamp,
  });
}
