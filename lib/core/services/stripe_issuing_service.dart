import 'package:dio/dio.dart';
import '../config/app_config.dart';
import '../errors/exceptions.dart';
import '../../domain/entities/virtual_card.dart';

class StripeIssuingService {
  final Dio _dio;
  late final String _secretKey;

  StripeIssuingService(this._dio) {
    _secretKey = AppConfig.stripeSecretKey;
  }

  // Create Cardholder
  Future<Map<String, dynamic>> createCardholder({
    required String name,
    required String email,
    required String phoneNumber,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _dio.post(
        'https://api.stripe.com/v1/issuing/cardholders',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: {
          'name': name,
          'email': email,
          'phone_number': phoneNumber,
          'type': 'individual',
          'status': 'active',
          if (metadata != null) ...metadata.map((k, v) => MapEntry('metadata[$k]', v)),
        },
      );

      return response.data;
    } on DioException catch (e) {
      throw CardException(
        message: 'Failed to create cardholder: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw CardException(
        message: 'Unexpected error creating cardholder: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Create Virtual Card
  Future<Map<String, dynamic>> createCard({
    required String cardholderId,
    required String currency,
    required VirtualCardType type,
    Map<String, dynamic>? spendingControls,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _dio.post(
        'https://api.stripe.com/v1/issuing/cards',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: {
          'cardholder': cardholderId,
          'currency': currency,
          'type': type == VirtualCardType.virtual ? 'virtual' : 'physical',
          'status': 'active',
          if (spendingControls != null) ...spendingControls,
          if (metadata != null) ...metadata.map((k, v) => MapEntry('metadata[$k]', v)),
        },
      );

      return response.data;
    } on DioException catch (e) {
      throw CardException(
        message: 'Failed to create card: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw CardException(
        message: 'Unexpected error creating card: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Get Card Details
  Future<Map<String, dynamic>> getCard(String cardId) async {
    try {
      final response = await _dio.get(
        'https://api.stripe.com/v1/issuing/cards/$cardId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
          },
        ),
      );

      return response.data;
    } on DioException catch (e) {
      throw CardException(
        message: 'Failed to get card: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw CardException(
        message: 'Unexpected error getting card: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Update Card Status
  Future<Map<String, dynamic>> updateCard({
    required String cardId,
    String? status,
    Map<String, dynamic>? spendingControls,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (status != null) data['status'] = status;
      if (spendingControls != null) data.addAll(spendingControls);
      if (metadata != null) {
        data.addAll(metadata.map((k, v) => MapEntry('metadata[$k]', v)));
      }

      final response = await _dio.post(
        'https://api.stripe.com/v1/issuing/cards/$cardId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: data,
      );

      return response.data;
    } on DioException catch (e) {
      throw CardException(
        message: 'Failed to update card: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw CardException(
        message: 'Unexpected error updating card: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Get Card Details (including sensitive data)
  Future<Map<String, dynamic>> getCardDetails(String cardId) async {
    try {
      final response = await _dio.get(
        'https://api.stripe.com/v1/issuing/cards/$cardId/details',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
          },
        ),
      );

      return response.data;
    } on DioException catch (e) {
      throw CardException(
        message: 'Failed to get card details: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw CardException(
        message: 'Unexpected error getting card details: ${e.toString()}',
        code: 500,
      );
    }
  }

  // List Cards for Cardholder
  Future<List<Map<String, dynamic>>> listCards({
    String? cardholderId,
    String? status,
    int limit = 10,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'limit': limit,
      };
      
      if (cardholderId != null) queryParams['cardholder'] = cardholderId;
      if (status != null) queryParams['status'] = status;

      final response = await _dio.get(
        'https://api.stripe.com/v1/issuing/cards',
        queryParameters: queryParams,
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
          },
        ),
      );

      final data = response.data['data'] as List;
      return data.cast<Map<String, dynamic>>();
    } on DioException catch (e) {
      throw CardException(
        message: 'Failed to list cards: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw CardException(
        message: 'Unexpected error listing cards: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Create Authorization (for testing)
  Future<Map<String, dynamic>> createAuthorization({
    required String cardId,
    required int amount,
    required String currency,
    required String merchantName,
    required String merchantCategory,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _dio.post(
        'https://api.stripe.com/v1/test_helpers/issuing/authorizations',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: {
          'card': cardId,
          'amount': amount,
          'currency': currency,
          'merchant_data[name]': merchantName,
          'merchant_data[category]': merchantCategory,
          if (metadata != null) ...metadata.map((k, v) => MapEntry('metadata[$k]', v)),
        },
      );

      return response.data;
    } on DioException catch (e) {
      throw CardException(
        message: 'Failed to create authorization: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw CardException(
        message: 'Unexpected error creating authorization: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Convert spending controls to Stripe format
  Map<String, dynamic> _convertSpendingControlsToStripe(List<SpendingControl> controls) {
    final result = <String, dynamic>{};
    
    for (int i = 0; i < controls.length; i++) {
      final control = controls[i];
      final prefix = 'spending_controls[$i]';
      
      switch (control.type) {
        case SpendingControlType.amount:
          if (control.amount != null) {
            result['${prefix}[amount]'] = (control.amount! * 100).round();
            result['${prefix}[interval]'] = _mapSpendingInterval(control.interval);
          }
          break;
        case SpendingControlType.category:
          if (control.categories != null && control.categories!.isNotEmpty) {
            for (int j = 0; j < control.categories!.length; j++) {
              result['${prefix}[allowed_categories][$j]'] = control.categories![j];
            }
          }
          break;
        case SpendingControlType.merchant:
          if (control.allowedMerchants != null && control.allowedMerchants!.isNotEmpty) {
            for (int j = 0; j < control.allowedMerchants!.length; j++) {
              result['${prefix}[allowed_merchants][$j]'] = control.allowedMerchants![j];
            }
          }
          if (control.blockedMerchants != null && control.blockedMerchants!.isNotEmpty) {
            for (int j = 0; j < control.blockedMerchants!.length; j++) {
              result['${prefix}[blocked_merchants][$j]'] = control.blockedMerchants![j];
            }
          }
          break;
        default:
          break;
      }
    }
    
    return result;
  }

  String _mapSpendingInterval(SpendingInterval? interval) {
    switch (interval) {
      case SpendingInterval.perTransaction:
        return 'per_authorization';
      case SpendingInterval.daily:
        return 'daily';
      case SpendingInterval.weekly:
        return 'weekly';
      case SpendingInterval.monthly:
        return 'monthly';
      case SpendingInterval.yearly:
        return 'yearly';
      case SpendingInterval.allTime:
        return 'all_time';
      default:
        return 'per_authorization';
    }
  }
}
