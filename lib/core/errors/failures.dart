import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final int? code;
  
  const Failure({
    required this.message,
    this.code,
  });
  
  @override
  List<Object?> get props => [message, code];
}

// General failures
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
  });
}

class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
  });
}

class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code,
  });
}

class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.code,
  });
}

// Authentication failures
class AuthFailure extends Failure {
  const AuthFailure({
    required super.message,
    super.code,
  });
}

class UnauthorizedFailure extends AuthFailure {
  const UnauthorizedFailure({
    super.message = 'Unauthorized access',
    super.code = 401,
  });
}

class ForbiddenFailure extends AuthFailure {
  const ForbiddenFailure({
    super.message = 'Access forbidden',
    super.code = 403,
  });
}

// Payment failures
class PaymentFailure extends Failure {
  const PaymentFailure({
    required super.message,
    super.code,
  });
}

class InsufficientFundsFailure extends PaymentFailure {
  const InsufficientFundsFailure({
    super.message = 'Insufficient funds',
    super.code = 4001,
  });
}

class CardDeclinedFailure extends PaymentFailure {
  const CardDeclinedFailure({
    super.message = 'Card declined',
    super.code = 4002,
  });
}

class PaymentMethodFailure extends PaymentFailure {
  const PaymentMethodFailure({
    super.message = 'Invalid payment method',
    super.code = 4003,
  });
}

// Wallet failures
class WalletFailure extends Failure {
  const WalletFailure({
    required super.message,
    super.code,
  });
}

class WalletNotFoundFailure extends WalletFailure {
  const WalletNotFoundFailure({
    super.message = 'Wallet not found',
    super.code = 5001,
  });
}

class WalletLockedFailure extends WalletFailure {
  const WalletLockedFailure({
    super.message = 'Wallet is locked',
    super.code = 5002,
  });
}

class InvitationFailure extends WalletFailure {
  const InvitationFailure({
    super.message = 'Invalid invitation',
    super.code = 5003,
  });
}

// Card failures
class CardFailure extends Failure {
  const CardFailure({
    required super.message,
    super.code,
  });
}

class CardNotFoundFailure extends CardFailure {
  const CardNotFoundFailure({
    super.message = 'Card not found',
    super.code = 6001,
  });
}

class CardBlockedFailure extends CardFailure {
  const CardBlockedFailure({
    super.message = 'Card is blocked',
    super.code = 6002,
  });
}

class SpendingLimitExceededFailure extends CardFailure {
  const SpendingLimitExceededFailure({
    super.message = 'Spending limit exceeded',
    super.code = 6003,
  });
}
