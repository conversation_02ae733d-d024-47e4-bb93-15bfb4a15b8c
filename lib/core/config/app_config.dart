import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConfig {
  // Supabase Configuration
  static String get supabaseUrl => dotenv.env['SUPABASE_URL'] ?? '';
  static String get supabaseAnonKey => dotenv.env['SUPABASE_ANON_KEY'] ?? '';
  static String get supabaseServiceKey => dotenv.env['SUPABASE_SERVICE_KEY'] ?? '';
  
  // Stripe Configuration
  static String get stripePublishableKey => dotenv.env['STRIPE_PUBLISHABLE_KEY'] ?? '';
  static String get stripeSecretKey => dotenv.env['STRIPE_SECRET_KEY'] ?? '';
  static String get stripeWebhookSecret => dotenv.env['STRIPE_WEBHOOK_SECRET'] ?? '';
  
  // Firebase Configuration
  static String get firebaseProjectId => dotenv.env['FIREBASE_PROJECT_ID'] ?? '';
  static String get firebaseApiKey => dotenv.env['FIREBASE_API_KEY'] ?? '';
  
  // App Configuration
  static const String appName = 'Potto';
  static const String appVersion = '1.0.0';
  static const String supportEmail = '<EMAIL>';
  
  // API Configuration
  static const Duration apiTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  
  // Card Configuration
  static const double defaultSpendingLimit = 1000.0;
  static const String defaultCurrency = 'USD';
  
  // Validation
  static bool get isConfigValid {
    return supabaseUrl.isNotEmpty &&
           supabaseAnonKey.isNotEmpty &&
           stripePublishableKey.isNotEmpty;
  }
  
  // Environment Detection
  static bool get isProduction => dotenv.env['ENVIRONMENT'] == 'production';
  static bool get isDevelopment => dotenv.env['ENVIRONMENT'] == 'development';
  static bool get isStaging => dotenv.env['ENVIRONMENT'] == 'staging';
}
