import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'api_client.g.dart';

@RestApi()
abstract class ApiClient {
  factory ApiClient(Dio dio, {String baseUrl}) = _ApiClient;
  
  // Stripe Payment Intents
  @POST('/payment_intents')
  Future<Map<String, dynamic>> createPaymentIntent(
    @Body() Map<String, dynamic> data,
  );
  
  @POST('/payment_intents/{id}/confirm')
  Future<Map<String, dynamic>> confirmPaymentIntent(
    @Path('id') String paymentIntentId,
    @Body() Map<String, dynamic> data,
  );
  
  // Stripe Issuing - Cards
  @POST('/issuing/cards')
  Future<Map<String, dynamic>> createCard(
    @Body() Map<String, dynamic> data,
  );
  
  @GET('/issuing/cards/{id}')
  Future<Map<String, dynamic>> getCard(
    @Path('id') String cardId,
  );
  
  @POST('/issuing/cards/{id}')
  Future<Map<String, dynamic>> updateCard(
    @Path('id') String cardId,
    @Body() Map<String, dynamic> data,
  );
  
  @GET('/issuing/cards')
  Future<Map<String, dynamic>> listCards(
    @Query('limit') int? limit,
    @Query('starting_after') String? startingAfter,
  );
  
  // Stripe Issuing - Cardholders
  @POST('/issuing/cardholders')
  Future<Map<String, dynamic>> createCardholder(
    @Body() Map<String, dynamic> data,
  );
  
  @GET('/issuing/cardholders/{id}')
  Future<Map<String, dynamic>> getCardholder(
    @Path('id') String cardholderId,
  );
  
  @POST('/issuing/cardholders/{id}')
  Future<Map<String, dynamic>> updateCardholder(
    @Path('id') String cardholderId,
    @Body() Map<String, dynamic> data,
  );
  
  // Stripe Issuing - Transactions
  @GET('/issuing/transactions')
  Future<Map<String, dynamic>> listTransactions(
    @Query('card') String? cardId,
    @Query('cardholder') String? cardholderId,
    @Query('limit') int? limit,
    @Query('starting_after') String? startingAfter,
  );
  
  @GET('/issuing/transactions/{id}')
  Future<Map<String, dynamic>> getTransaction(
    @Path('id') String transactionId,
  );
  
  @POST('/issuing/transactions/{id}')
  Future<Map<String, dynamic>> updateTransaction(
    @Path('id') String transactionId,
    @Body() Map<String, dynamic> data,
  );
  
  // Stripe Issuing - Authorizations
  @GET('/issuing/authorizations')
  Future<Map<String, dynamic>> listAuthorizations(
    @Query('card') String? cardId,
    @Query('cardholder') String? cardholderId,
    @Query('limit') int? limit,
    @Query('starting_after') String? startingAfter,
  );
  
  @GET('/issuing/authorizations/{id}')
  Future<Map<String, dynamic>> getAuthorization(
    @Path('id') String authorizationId,
  );
  
  @POST('/issuing/authorizations/{id}/approve')
  Future<Map<String, dynamic>> approveAuthorization(
    @Path('id') String authorizationId,
    @Body() Map<String, dynamic> data,
  );
  
  @POST('/issuing/authorizations/{id}/decline')
  Future<Map<String, dynamic>> declineAuthorization(
    @Path('id') String authorizationId,
    @Body() Map<String, dynamic> data,
  );
  
  // Stripe Customers
  @POST('/customers')
  Future<Map<String, dynamic>> createCustomer(
    @Body() Map<String, dynamic> data,
  );
  
  @GET('/customers/{id}')
  Future<Map<String, dynamic>> getCustomer(
    @Path('id') String customerId,
  );
  
  @POST('/customers/{id}')
  Future<Map<String, dynamic>> updateCustomer(
    @Path('id') String customerId,
    @Body() Map<String, dynamic> data,
  );
  
  // Stripe Payment Methods
  @POST('/payment_methods')
  Future<Map<String, dynamic>> createPaymentMethod(
    @Body() Map<String, dynamic> data,
  );
  
  @GET('/payment_methods/{id}')
  Future<Map<String, dynamic>> getPaymentMethod(
    @Path('id') String paymentMethodId,
  );
  
  @POST('/payment_methods/{id}/attach')
  Future<Map<String, dynamic>> attachPaymentMethod(
    @Path('id') String paymentMethodId,
    @Body() Map<String, dynamic> data,
  );
  
  @POST('/payment_methods/{id}/detach')
  Future<Map<String, dynamic>> detachPaymentMethod(
    @Path('id') String paymentMethodId,
  );
  
  // Stripe Setup Intents
  @POST('/setup_intents')
  Future<Map<String, dynamic>> createSetupIntent(
    @Body() Map<String, dynamic> data,
  );
  
  @POST('/setup_intents/{id}/confirm')
  Future<Map<String, dynamic>> confirmSetupIntent(
    @Path('id') String setupIntentId,
    @Body() Map<String, dynamic> data,
  );
}
