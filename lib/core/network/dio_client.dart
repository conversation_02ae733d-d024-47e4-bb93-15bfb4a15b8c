import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../config/app_config.dart';
import '../errors/exceptions.dart';

class DioClient {
  static Dio createDio() {
    final dio = Dio();
    
    // Base options
    dio.options = BaseOptions(
      baseUrl: AppConfig.isProduction 
          ? 'https://api.potto.app' 
          : 'https://api-staging.potto.app',
      connectTimeout: AppConfig.apiTimeout,
      receiveTimeout: AppConfig.apiTimeout,
      sendTimeout: AppConfig.apiTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );
    
    // Add interceptors
    dio.interceptors.add(_createLogInterceptor());
    dio.interceptors.add(_createErrorInterceptor());
    dio.interceptors.add(_createAuthInterceptor());
    
    return dio;
  }
  
  static LogInterceptor _createLogInterceptor() {
    return LogInterceptor(
      request: kDebugMode,
      requestHeader: kDebugMode,
      requestBody: kDebugMode,
      responseHeader: kDebugMode,
      responseBody: kDebugMode,
      error: kDebugMode,
      logPrint: (object) {
        if (kDebugMode) {
          print('[DIO] $object');
        }
      },
    );
  }
  
  static InterceptorsWrapper _createErrorInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) {
        final exception = _handleDioError(error);
        handler.reject(
          DioException(
            requestOptions: error.requestOptions,
            error: exception,
            type: error.type,
            response: error.response,
          ),
        );
      },
    );
  }
  
  static InterceptorsWrapper _createAuthInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        // This will be implemented when we have the auth system
        handler.next(options);
      },
      onResponse: (response, handler) {
        handler.next(response);
      },
      onError: (error, handler) {
        if (error.response?.statusCode == 401) {
          // Handle token refresh or logout
          // This will be implemented when we have the auth system
        }
        handler.next(error);
      },
    );
  }
  
  static AppException _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const TimeoutException();
        
      case DioExceptionType.badResponse:
        return _handleResponseError(error.response);
        
      case DioExceptionType.cancel:
        return const AppException(
          message: 'Request cancelled',
          code: 499,
        );
        
      case DioExceptionType.connectionError:
        return const NetworkException(
          message: 'Network connection error',
          code: 0,
        );
        
      case DioExceptionType.badCertificate:
        return const NetworkException(
          message: 'Certificate verification failed',
          code: 0,
        );
        
      case DioExceptionType.unknown:
      default:
        return AppException(
          message: error.message ?? 'Unknown error occurred',
          code: 0,
        );
    }
  }
  
  static AppException _handleResponseError(Response? response) {
    if (response == null) {
      return const ServerException(
        message: 'No response from server',
        code: 0,
      );
    }
    
    final statusCode = response.statusCode ?? 0;
    final data = response.data;
    
    String message = 'Server error';
    if (data is Map<String, dynamic> && data.containsKey('message')) {
      message = data['message'] as String;
    } else if (data is Map<String, dynamic> && data.containsKey('error')) {
      message = data['error'] as String;
    }
    
    switch (statusCode) {
      case 400:
        return ValidationException(
          message: message,
          code: statusCode,
          data: data,
        );
        
      case 401:
        return UnauthorizedException(
          message: message,
          code: statusCode,
          data: data,
        );
        
      case 403:
        return ForbiddenException(
          message: message,
          code: statusCode,
          data: data,
        );
        
      case 404:
        return ServerException(
          message: 'Resource not found',
          code: statusCode,
          data: data,
        );
        
      case 422:
        return ValidationException(
          message: message,
          code: statusCode,
          data: data,
        );
        
      case 429:
        return ServerException(
          message: 'Too many requests',
          code: statusCode,
          data: data,
        );
        
      case 500:
      case 502:
      case 503:
      case 504:
        return ServerException(
          message: 'Server error',
          code: statusCode,
          data: data,
        );
        
      default:
        return ServerException(
          message: message,
          code: statusCode,
          data: data,
        );
    }
  }
}
