class AppConstants {
  // App Information
  static const String appName = 'Potto';
  static const String appTagline = 'Pool money, spend together';
  static const String appDescription = 'Group wallet app with virtual cards for shared spending';
  
  // API Endpoints
  static const String baseUrl = 'https://api.potto.app';
  static const String stripeApiUrl = 'https://api.stripe.com/v1';
  
  // Database Tables
  static const String usersTable = 'users';
  static const String walletsTable = 'wallets';
  static const String walletMembersTable = 'wallet_members';
  static const String transactionsTable = 'transactions';
  static const String cardsTable = 'cards';
  static const String invitationsTable = 'invitations';
  static const String notificationsTable = 'notifications';
  static const String messagesTable = 'messages';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userIdKey = 'user_id';
  static const String themeKey = 'theme_mode';
  static const String onboardingKey = 'onboarding_completed';
  static const String biometricKey = 'biometric_enabled';
  static const String notificationsKey = 'notifications_enabled';
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxWalletNameLength = 50;
  static const int maxDescriptionLength = 200;
  static const double minContributionAmount = 1.0;
  static const double maxContributionAmount = 10000.0;
  static const double minSpendingLimit = 10.0;
  static const double maxSpendingLimit = 5000.0;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Timeouts
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration cacheTimeout = Duration(hours: 1);
  static const Duration sessionTimeout = Duration(hours: 24);
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Notification Types
  static const String transactionNotification = 'transaction';
  static const String invitationNotification = 'invitation';
  static const String walletNotification = 'wallet';
  static const String cardNotification = 'card';
  static const String messageNotification = 'message';
  
  // Card Types
  static const String virtualCard = 'virtual';
  static const String physicalCard = 'physical';
  
  // Transaction Types
  static const String contribution = 'contribution';
  static const String spending = 'spending';
  static const String refund = 'refund';
  static const String fee = 'fee';
  
  // Wallet Status
  static const String walletActive = 'active';
  static const String walletLocked = 'locked';
  static const String walletClosed = 'closed';
  
  // Member Roles
  static const String adminRole = 'admin';
  static const String memberRole = 'member';
  static const String viewerRole = 'viewer';
  
  // Currency
  static const String defaultCurrency = 'USD';
  static const List<String> supportedCurrencies = ['USD', 'EUR', 'GBP', 'CAD'];
  
  // Deep Links
  static const String inviteScheme = 'potto://invite';
  static const String walletScheme = 'potto://wallet';
  static const String cardScheme = 'potto://card';
  
  // Error Messages
  static const String genericError = 'Something went wrong. Please try again.';
  static const String networkError = 'Network error. Please check your connection.';
  static const String authError = 'Authentication failed. Please login again.';
  static const String permissionError = 'You don\'t have permission to perform this action.';
  static const String validationError = 'Please check your input and try again.';
  
  // Success Messages
  static const String walletCreated = 'Wallet created successfully!';
  static const String inviteSent = 'Invitation sent successfully!';
  static const String paymentProcessed = 'Payment processed successfully!';
  static const String cardCreated = 'Virtual card created successfully!';
}
