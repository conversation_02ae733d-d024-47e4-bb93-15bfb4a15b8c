import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../widgets/common/custom_card.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/empty_state.dart';
import '../../widgets/common/app_bar.dart';

class ChatListScreen extends StatefulWidget {
  const ChatListScreen({super.key});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  final List<ChatItem> _chats = [
    ChatItem(
      id: '1',
      name: 'Weekend Trip Fund',
      lastMessage: 'Sarah added \$50 to the wallet',
      timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      unreadCount: 2,
      isGroup: true,
      avatarUrl: null,
    ),
    ChatItem(
      id: '2',
      name: '<PERSON>',
      lastMessage: 'Thanks for splitting the dinner bill!',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      unreadCount: 0,
      isGroup: false,
      avatarUrl: null,
    ),
    ChatItem(
      id: '3',
      name: 'House Expenses',
      lastMessage: 'Mike: Paid the electricity bill',
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      unreadCount: 1,
      isGroup: true,
      avatarUrl: null,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Messages',
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Implement search functionality
            },
          ),
          IconButton(
            icon: const Icon(Icons.add_comment_outlined),
            onPressed: () {
              // TODO: Start new chat
            },
          ),
        ],
      ),
      body: _chats.isEmpty ? _buildEmptyState() : _buildChatList(),
    );
  }

  Widget _buildEmptyState() {
    return EmptyState(
      icon: Icons.chat_outlined,
      title: 'No Messages Yet',
      subtitle: 'Start chatting with your wallet members to coordinate expenses and payments',
      customAction: CustomButton(
        text: 'Start New Chat',
        icon: const Icon(Icons.add_comment, size: 20),
        variant: ButtonVariant.primary,
        onPressed: () {
          // TODO: Start new chat functionality
        },
      ),
    );
  }

  Widget _buildChatList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _chats.length,
      itemBuilder: (context, index) {
        final chat = _chats[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildChatItem(chat),
        );
      },
    );
  }

  Widget _buildChatItem(ChatItem chat) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return CustomCard(
      variant: CardVariant.elevated,
      onTap: () => context.push('/chat/${chat.id}'),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Avatar
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: chat.isGroup 
                    ? colorScheme.primaryContainer 
                    : colorScheme.secondaryContainer,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                chat.isGroup ? Icons.group : Icons.person,
                color: chat.isGroup 
                    ? colorScheme.onPrimaryContainer 
                    : colorScheme.onSecondaryContainer,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            
            // Chat info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          chat.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        _formatTimestamp(chat.timestamp),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          chat.lastMessage,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: chat.unreadCount > 0 
                                ? colorScheme.onSurface 
                                : colorScheme.onSurfaceVariant,
                            fontWeight: chat.unreadCount > 0 
                                ? FontWeight.w500 
                                : FontWeight.w400,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (chat.unreadCount > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            chat.unreadCount.toString(),
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: colorScheme.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }
}

class ChatItem {
  final String id;
  final String name;
  final String lastMessage;
  final DateTime timestamp;
  final int unreadCount;
  final bool isGroup;
  final String? avatarUrl;

  ChatItem({
    required this.id,
    required this.name,
    required this.lastMessage,
    required this.timestamp,
    required this.unreadCount,
    required this.isGroup,
    this.avatarUrl,
  });
}
