import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/virtual_card/virtual_card_bloc.dart';
import '../../blocs/virtual_card/virtual_card_event.dart';
import '../../blocs/virtual_card/virtual_card_state.dart';
import '../../../domain/entities/virtual_card.dart';

class VirtualCardListScreen extends StatefulWidget {
  final String? walletId;
  final String? userId;

  const VirtualCardListScreen({
    Key? key,
    this.walletId,
    this.userId,
  }) : super(key: key);

  @override
  State<VirtualCardListScreen> createState() => _VirtualCardListScreenState();
}

class _VirtualCardListScreenState extends State<VirtualCardListScreen> {
  @override
  void initState() {
    super.initState();
    _loadCards();
  }

  void _loadCards() {
    context.read<VirtualCardBloc>().add(
          LoadVirtualCardsRequested(
            walletId: widget.walletId,
            userId: widget.userId,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Virtual Cards'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateCardDialog(),
          ),
        ],
      ),
      body: BlocConsumer<VirtualCardBloc, VirtualCardState>(
        listener: (context, state) {
          if (state.status == VirtualCardStatus.error) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'An error occurred'),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state.status == VirtualCardStatus.created) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Virtual card created successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state.status == VirtualCardStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.cards.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.credit_card_off,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No virtual cards yet',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Tap the + button to create your first card',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async => _loadCards(),
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: state.cards.length,
              itemBuilder: (context, index) {
                final card = state.cards[index];
                return VirtualCardTile(
                  card: card,
                  onTap: () => _showCardDetails(card),
                  onStatusToggle: () => _toggleCardStatus(card),
                );
              },
            ),
          );
        },
      ),
    );
  }

  void _showCreateCardDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateVirtualCardDialog(
        walletId: widget.walletId ?? '',
        onCardCreated: () => _loadCards(),
      ),
    );
  }

  void _showCardDetails(VirtualCard card) {
    // Navigate to card details screen
    // Navigator.pushNamed(context, '/virtual-card-details', arguments: card);
  }

  void _toggleCardStatus(VirtualCard card) {
    if (card.status == VirtualCardStatus.active) {
      context.read<VirtualCardBloc>().add(DeactivateCardRequested(card.id));
    } else if (card.status == VirtualCardStatus.inactive) {
      context.read<VirtualCardBloc>().add(ActivateCardRequested(card.id));
    }
  }
}

class VirtualCardTile extends StatelessWidget {
  final VirtualCard card;
  final VoidCallback onTap;
  final VoidCallback onStatusToggle;

  const VirtualCardTile({
    Key? key,
    required this.card,
    required this.onTap,
    required this.onStatusToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(),
          child: Icon(
            Icons.credit_card,
            color: Colors.white,
          ),
        ),
        title: Text(
          card.displayName ?? 'Virtual Card',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('**** **** **** ${card.last4}'),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    card.status.name.toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  card.type.name.toUpperCase(),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'toggle_status':
                onStatusToggle();
                break;
              case 'details':
                onTap();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'toggle_status',
              child: Text(
                card.status == VirtualCardStatus.active
                    ? 'Deactivate'
                    : 'Activate',
              ),
            ),
            const PopupMenuItem(
              value: 'details',
              child: Text('View Details'),
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  Color _getStatusColor() {
    switch (card.status) {
      case VirtualCardStatus.active:
        return Colors.green;
      case VirtualCardStatus.inactive:
        return Colors.orange;
      case VirtualCardStatus.canceled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

class CreateVirtualCardDialog extends StatefulWidget {
  final String walletId;
  final VoidCallback onCardCreated;

  const CreateVirtualCardDialog({
    Key? key,
    required this.walletId,
    required this.onCardCreated,
  }) : super(key: key);

  @override
  State<CreateVirtualCardDialog> createState() => _CreateVirtualCardDialogState();
}

class _CreateVirtualCardDialogState extends State<CreateVirtualCardDialog> {
  final _formKey = GlobalKey<FormState>();
  final _displayNameController = TextEditingController();
  VirtualCardType _selectedType = VirtualCardType.single_use;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Virtual Card'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'Card Name',
                hintText: 'Enter a name for this card',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a card name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<VirtualCardType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Card Type',
              ),
              items: VirtualCardType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.name.replaceAll('_', ' ').toUpperCase()),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedType = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _createCard,
          child: const Text('Create'),
        ),
      ],
    );
  }

  void _createCard() {
    if (_formKey.currentState!.validate()) {
      final request = CreateVirtualCardRequest(
        walletId: widget.walletId,
        displayName: _displayNameController.text.trim(),
        type: _selectedType,
      );

      context.read<VirtualCardBloc>().add(CreateVirtualCardRequested(request));
      Navigator.of(context).pop();
      widget.onCardCreated();
    }
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    super.dispose();
  }
}
