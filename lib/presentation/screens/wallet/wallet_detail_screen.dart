import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../domain/entities/wallet.dart';
import '../../blocs/wallet/wallet_bloc.dart';
import '../../blocs/wallet/wallet_event.dart';
import '../../blocs/wallet/wallet_state.dart';
import '../../blocs/realtime/realtime_bloc.dart';
import '../../blocs/realtime/realtime_event.dart';
import '../../blocs/realtime/realtime_state.dart';
import '../chat/chat_screen.dart';

class WalletDetailScreen extends StatefulWidget {
  final String walletId;

  const WalletDetailScreen({super.key, required this.walletId});

  @override
  State<WalletDetailScreen> createState() => _WalletDetailScreenState();
}

class _WalletDetailScreenState extends State<WalletDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    context.read<WalletBloc>().add(LoadWallet(widget.walletId));

    // Subscribe to real-time wallet updates
    context.read<RealtimeBloc>().add(SubscribeToWallet(widget.walletId));
  }

  @override
  void dispose() {
    _tabController.dispose();
    // Unsubscribe from real-time updates
    context.read<RealtimeBloc>().add(UnsubscribeFromWallet(widget.walletId));
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MultiBlocListener(
        listeners: [
          BlocListener<WalletBloc, WalletState>(
            listener: (context, state) {
              if (state is WalletError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<RealtimeBloc, RealtimeState>(
            listener: (context, state) {
              if (state is WalletRealtimeEventReceived) {
                // Refresh wallet data when real-time updates are received
                context.read<WalletBloc>().add(LoadWallet(widget.walletId));
              }
            },
          ),
        ],
        child: BlocBuilder<WalletBloc, WalletState>(
          builder: (context, state) {
            if (state is WalletLoading) {
              return const Scaffold(
                body: Center(child: CircularProgressIndicator()),
              );
            }

            if (state is WalletLoaded) {
              return _buildWalletDetail(state.wallet);
            }

            return const Scaffold(
              body: Center(child: Text('Wallet not found')),
            );
          },
        ),
      ),
    );
  }

  Widget _buildWalletDetail(Wallet wallet) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          SliverAppBar(
            expandedHeight: 200,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(wallet.name),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _getWalletColor(wallet.type),
                      _getWalletColor(wallet.type).withOpacity(0.7),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 40), // Space for title
                        Row(
                          children: [
                            Icon(
                              _getWalletIcon(wallet.type),
                              color: Colors.white,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _getWalletTypeLabel(wallet.type),
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${wallet.currency} ${wallet.currentAmount.toStringAsFixed(2)}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (wallet.goalAmount > 0) ...[
                          const SizedBox(height: 4),
                          Text(
                            'Goal: ${wallet.currency} ${wallet.goalAmount.toStringAsFixed(2)}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
            actions: [
              PopupMenuButton<String>(
                onSelected: (value) => _handleMenuAction(value, wallet),
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'invite',
                        child: ListTile(
                          leading: Icon(Icons.person_add),
                          title: Text('Invite Member'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'settings',
                        child: ListTile(
                          leading: Icon(Icons.settings),
                          title: Text('Settings'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Overview'),
                Tab(text: 'Members'),
                Tab(text: 'Activity'),
              ],
            ),
          ),
        ];
      },
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(wallet),
          _buildMembersTab(wallet),
          _buildActivityTab(wallet),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _openChat(wallet),
        child: const Icon(Icons.chat),
        tooltip: 'Group Chat',
      ),
    );
  }

  Widget _buildOverviewTab(Wallet wallet) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (wallet.description.isNotEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Description',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(wallet.description),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
          if (wallet.goalAmount > 0) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Goal Progress',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    LinearProgressIndicator(
                      value: (wallet.currentAmount / wallet.goalAmount).clamp(
                        0.0,
                        1.0,
                      ),
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        wallet.currentAmount >= wallet.goalAmount
                            ? Colors.green
                            : Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${((wallet.currentAmount / wallet.goalAmount) * 100).toStringAsFixed(1)}% complete',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        Text(
                          '${wallet.currency} ${(wallet.goalAmount - wallet.currentAmount).toStringAsFixed(2)} remaining',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _showContributeDialog(wallet),
                  icon: const Icon(Icons.add),
                  label: const Text('Contribute'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showSpendDialog(wallet),
                  icon: const Icon(Icons.payment),
                  label: const Text('Spend'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMembersTab(Wallet wallet) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: wallet.members.length,
      itemBuilder: (context, index) {
        final member = wallet.members[index];
        return Card(
          child: ListTile(
            leading: CircleAvatar(child: Text(member.email[0].toUpperCase())),
            title: Text(member.email),
            subtitle: Text(_getRoleLabel(member.role)),
            trailing: _buildMemberActions(member, wallet),
          ),
        );
      },
    );
  }

  Widget _buildActivityTab(Wallet wallet) {
    return const Center(child: Text('Activity feed coming soon'));
  }

  Widget? _buildMemberActions(WalletMember member, Wallet wallet) {
    return PopupMenuButton<String>(
      onSelected: (value) => _handleMemberAction(value, member, wallet),
      itemBuilder:
          (context) => [
            const PopupMenuItem(
              value: 'change_role',
              child: Text('Change Role'),
            ),
            const PopupMenuItem(
              value: 'remove',
              child: Text('Remove Member', style: TextStyle(color: Colors.red)),
            ),
          ],
    );
  }

  void _handleMenuAction(String action, Wallet wallet) {
    switch (action) {
      case 'invite':
        _showInviteDialog(wallet);
        break;
      case 'settings':
        context.push('/wallet/${wallet.id}/settings');
        break;
    }
  }

  void _handleMemberAction(String action, WalletMember member, Wallet wallet) {
    switch (action) {
      case 'change_role':
        _showChangeRoleDialog(member, wallet);
        break;
      case 'remove':
        _showRemoveMemberDialog(member, wallet);
        break;
    }
  }

  void _showContributeDialog(Wallet wallet) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Contribute feature coming soon')),
    );
  }

  void _showSpendDialog(Wallet wallet) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Spend feature coming soon')));
  }

  void _showInviteDialog(Wallet wallet) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Invite feature coming soon')));
  }

  void _showChangeRoleDialog(WalletMember member, Wallet wallet) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Change role feature coming soon')),
    );
  }

  void _showRemoveMemberDialog(WalletMember member, Wallet wallet) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Remove member feature coming soon')),
    );
  }

  Color _getWalletColor(WalletType type) {
    switch (type) {
      case WalletType.personal:
        return Colors.blue;
      case WalletType.group:
        return Colors.purple;
      case WalletType.savings:
        return Colors.green;
      case WalletType.project:
        return Colors.orange;
    }
  }

  IconData _getWalletIcon(WalletType type) {
    switch (type) {
      case WalletType.personal:
        return Icons.person;
      case WalletType.group:
        return Icons.group;
      case WalletType.savings:
        return Icons.savings;
      case WalletType.project:
        return Icons.work;
    }
  }

  String _getWalletTypeLabel(WalletType type) {
    switch (type) {
      case WalletType.personal:
        return 'Personal Wallet';
      case WalletType.group:
        return 'Group Wallet';
      case WalletType.savings:
        return 'Savings Wallet';
      case WalletType.project:
        return 'Project Wallet';
    }
  }

  String _getRoleLabel(WalletMemberRole role) {
    switch (role) {
      case WalletMemberRole.owner:
        return 'Owner';
      case WalletMemberRole.admin:
        return 'Admin';
      case WalletMemberRole.member:
        return 'Member';
    }
  }

  void _openChat(Wallet wallet) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          walletId: wallet.id,
          walletName: wallet.name,
        ),
      ),
    );
  }
}
