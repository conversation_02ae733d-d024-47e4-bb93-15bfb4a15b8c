import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../widgets/common/custom_card.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/empty_state.dart';
import '../../widgets/common/app_bar.dart';

class VirtualCardListScreen extends StatefulWidget {
  const VirtualCardListScreen({super.key});

  @override
  State<VirtualCardListScreen> createState() => _VirtualCardListScreenState();
}

class _VirtualCardListScreenState extends State<VirtualCardListScreen> {
  final List<VirtualCard> _cards = [
    VirtualCard(
      id: '1',
      name: 'Weekend Trip Card',
      lastFourDigits: '4532',
      balance: 250.00,
      spentThisMonth: 180.50,
      monthlyLimit: 500.00,
      status: CardStatus.active,
      expiryDate: DateTime(2027, 12, 31),
      cardType: CardType.visa,
    ),
    VirtualCard(
      id: '2',
      name: 'Groceries Card',
      lastFourDigits: '8901',
      balance: 75.25,
      spentThisMonth: 324.75,
      monthlyLimit: 400.00,
      status: CardStatus.active,
      expiryDate: DateTime(2026, 8, 31),
      cardType: CardType.mastercard,
    ),
    VirtualCard(
      id: '3',
      name: 'Emergency Fund Card',
      lastFourDigits: '2468',
      balance: 1000.00,
      spentThisMonth: 0.00,
      monthlyLimit: 1000.00,
      status: CardStatus.frozen,
      expiryDate: DateTime(2028, 3, 31),
      cardType: CardType.visa,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Virtual Cards',
        actions: [
          IconButton(
            icon: const Icon(Icons.add_card),
            onPressed: () => context.push('/cards/create'),
          ),
        ],
      ),
      body: _cards.isEmpty ? _buildEmptyState() : _buildCardList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/cards/create'),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return EmptyState(
      icon: Icons.credit_card_outlined,
      title: 'No Virtual Cards',
      subtitle: 'Create virtual cards to manage spending for different categories and wallets',
      customAction: CustomButton(
        text: 'Create Virtual Card',
        icon: const Icon(Icons.add_card, size: 20),
        variant: ButtonVariant.primary,
        onPressed: () => context.push('/cards/create'),
      ),
    );
  }

  Widget _buildCardList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _cards.length,
      itemBuilder: (context, index) {
        final card = _cards[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildCardItem(card),
        );
      },
    );
  }

  Widget _buildCardItem(VirtualCard card) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final spendingPercentage = card.monthlyLimit > 0 
        ? (card.spentThisMonth / card.monthlyLimit).clamp(0.0, 1.0)
        : 0.0;

    return CustomCard(
      variant: CardVariant.elevated,
      onTap: () => context.push('/cards/${card.id}'),
      child: Container(
        decoration: BoxDecoration(
          gradient: _getCardGradient(card.cardType, card.status),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Card header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      card.name,
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildStatusChip(card.status),
                ],
              ),
              const SizedBox(height: 24),

              // Card number
              Text(
                '**** **** **** ${card.lastFourDigits}',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 2,
                ),
              ),
              const SizedBox(height: 16),

              // Card details
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Balance',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                      Text(
                        '\$${card.balance.toStringAsFixed(2)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Expires',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                      Text(
                        '${card.expiryDate.month.toString().padLeft(2, '0')}/${card.expiryDate.year.toString().substring(2)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  _getCardTypeIcon(card.cardType),
                ],
              ),
              const SizedBox(height: 20),

              // Spending progress
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'This Month',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          '\$${card.spentThisMonth.toStringAsFixed(2)} / \$${card.monthlyLimit.toStringAsFixed(2)}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: spendingPercentage,
                      backgroundColor: Colors.white.withOpacity(0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        spendingPercentage > 0.8 
                            ? Colors.red.shade300
                            : Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(CardStatus status) {
    Color color;
    String label;
    IconData icon;

    switch (status) {
      case CardStatus.active:
        color = Colors.green;
        label = 'Active';
        icon = Icons.check_circle;
        break;
      case CardStatus.frozen:
        color = Colors.blue;
        label = 'Frozen';
        icon = Icons.ac_unit;
        break;
      case CardStatus.expired:
        color = Colors.red;
        label = 'Expired';
        icon = Icons.error;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  LinearGradient _getCardGradient(CardType cardType, CardStatus status) {
    if (status == CardStatus.frozen) {
      return LinearGradient(
        colors: [Colors.blue.shade400, Colors.blue.shade600],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }

    switch (cardType) {
      case CardType.visa:
        return LinearGradient(
          colors: [Colors.purple.shade400, Colors.purple.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case CardType.mastercard:
        return LinearGradient(
          colors: [Colors.orange.shade400, Colors.red.shade500],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  Widget _getCardTypeIcon(CardType cardType) {
    switch (cardType) {
      case CardType.visa:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Text(
            'VISA',
            style: TextStyle(
              color: Colors.blue,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        );
      case CardType.mastercard:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Text(
            'MC',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        );
    }
  }
}

enum CardStatus { active, frozen, expired }
enum CardType { visa, mastercard }

class VirtualCard {
  final String id;
  final String name;
  final String lastFourDigits;
  final double balance;
  final double spentThisMonth;
  final double monthlyLimit;
  final CardStatus status;
  final DateTime expiryDate;
  final CardType cardType;

  VirtualCard({
    required this.id,
    required this.name,
    required this.lastFourDigits,
    required this.balance,
    required this.spentThisMonth,
    required this.monthlyLimit,
    required this.status,
    required this.expiryDate,
    required this.cardType,
  });
}
