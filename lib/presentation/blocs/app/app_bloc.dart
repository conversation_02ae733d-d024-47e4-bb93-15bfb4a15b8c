import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../core/services/storage_service.dart';

part 'app_event.dart';
part 'app_state.dart';

class AppBloc extends Bloc<AppEvent, AppState> {
  final StorageService _storageService;

  AppBloc(this._storageService) : super(const AppState()) {
    on<AppStarted>(_onAppStarted);
    on<ThemeChanged>(_onThemeChanged);
    on<LocaleChanged>(_onLocaleChanged);
  }

  Future<void> _onAppStarted(AppStarted event, Emitter<AppState> emit) async {
    // Load saved theme mode
    final savedTheme = _storageService.getThemeMode();
    ThemeMode themeMode = ThemeMode.system;
    
    if (savedTheme != null) {
      switch (savedTheme) {
        case 'light':
          themeMode = ThemeMode.light;
          break;
        case 'dark':
          themeMode = ThemeMode.dark;
          break;
        case 'system':
        default:
          themeMode = ThemeMode.system;
          break;
      }
    }

    emit(state.copyWith(
      themeMode: themeMode,
      isInitialized: true,
    ));
  }

  Future<void> _onThemeChanged(ThemeChanged event, Emitter<AppState> emit) async {
    await _storageService.setThemeMode(event.themeMode.name);
    emit(state.copyWith(themeMode: event.themeMode));
  }

  Future<void> _onLocaleChanged(LocaleChanged event, Emitter<AppState> emit) async {
    emit(state.copyWith(locale: event.locale));
  }
}
