part of 'app_bloc.dart';

class AppState extends Equatable {
  final ThemeMode themeMode;
  final Locale? locale;
  final bool isInitialized;

  const AppState({
    this.themeMode = ThemeMode.system,
    this.locale,
    this.isInitialized = false,
  });

  AppState copyWith({
    ThemeMode? themeMode,
    Locale? locale,
    bool? isInitialized,
  }) {
    return AppState(
      themeMode: themeMode ?? this.themeMode,
      locale: locale ?? this.locale,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }

  @override
  List<Object?> get props => [themeMode, locale, isInitialized];
}
