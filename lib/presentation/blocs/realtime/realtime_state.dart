import 'package:equatable/equatable.dart';
import '../../../core/services/chat_service.dart';
import '../../../core/services/realtime_service.dart';

/// Base class for realtime states
abstract class RealtimeState extends Equatable {
  const RealtimeState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class RealtimeInitial extends RealtimeState {
  const RealtimeInitial();
}

/// Loading state
class RealtimeLoading extends RealtimeState {
  const RealtimeLoading();
}

/// Realtime services initialized
class RealtimeInitialized extends RealtimeState {
  final String userId;
  final Set<String> subscribedWallets;
  final Set<String> subscribedChats;
  final bool notificationsSubscribed;
  final bool cardsSubscribed;

  const RealtimeInitialized({
    required this.userId,
    this.subscribedWallets = const {},
    this.subscribedChats = const {},
    this.notificationsSubscribed = false,
    this.cardsSubscribed = false,
  });

  @override
  List<Object?> get props => [
    userId,
    subscribedWallets,
    subscribedChats,
    notificationsSubscribed,
    cardsSubscribed,
  ];

  RealtimeInitialized copyWith({
    String? userId,
    Set<String>? subscribedWallets,
    Set<String>? subscribedChats,
    bool? notificationsSubscribed,
    bool? cardsSubscribed,
  }) {
    return RealtimeInitialized(
      userId: userId ?? this.userId,
      subscribedWallets: subscribedWallets ?? this.subscribedWallets,
      subscribedChats: subscribedChats ?? this.subscribedChats,
      notificationsSubscribed: notificationsSubscribed ?? this.notificationsSubscribed,
      cardsSubscribed: cardsSubscribed ?? this.cardsSubscribed,
    );
  }
}

/// Chat messages loaded
class ChatMessagesLoaded extends RealtimeState {
  final String walletId;
  final List<ChatMessage> messages;
  final bool hasMore;
  final int unreadCount;

  const ChatMessagesLoaded({
    required this.walletId,
    required this.messages,
    this.hasMore = true,
    this.unreadCount = 0,
  });

  @override
  List<Object?> get props => [walletId, messages, hasMore, unreadCount];

  ChatMessagesLoaded copyWith({
    String? walletId,
    List<ChatMessage>? messages,
    bool? hasMore,
    int? unreadCount,
  }) {
    return ChatMessagesLoaded(
      walletId: walletId ?? this.walletId,
      messages: messages ?? this.messages,
      hasMore: hasMore ?? this.hasMore,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}

/// New chat message received
class NewChatMessageReceived extends RealtimeState {
  final String walletId;
  final ChatMessage message;

  const NewChatMessageReceived({
    required this.walletId,
    required this.message,
  });

  @override
  List<Object?> get props => [walletId, message];
}

/// Chat message sent successfully
class ChatMessageSent extends RealtimeState {
  final ChatMessage message;

  const ChatMessageSent(this.message);

  @override
  List<Object?> get props => [message];
}

/// Chat message updated
class ChatMessageUpdated extends RealtimeState {
  final ChatMessage message;

  const ChatMessageUpdated(this.message);

  @override
  List<Object?> get props => [message];
}

/// Chat message deleted
class ChatMessageDeleted extends RealtimeState {
  final String messageId;

  const ChatMessageDeleted(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Wallet realtime event received
class WalletRealtimeEventReceived extends RealtimeState {
  final WalletRealtimeEvent event;

  const WalletRealtimeEventReceived(this.event);

  @override
  List<Object?> get props => [event];
}

/// Notification realtime event received
class NotificationRealtimeEventReceived extends RealtimeState {
  final NotificationRealtimeEvent event;

  const NotificationRealtimeEventReceived(this.event);

  @override
  List<Object?> get props => [event];
}

/// Card realtime event received
class CardRealtimeEventReceived extends RealtimeState {
  final CardRealtimeEvent event;

  const CardRealtimeEventReceived(this.event);

  @override
  List<Object?> get props => [event];
}

/// Unread count updated
class UnreadCountUpdated extends RealtimeState {
  final String walletId;
  final int count;

  const UnreadCountUpdated({
    required this.walletId,
    required this.count,
  });

  @override
  List<Object?> get props => [walletId, count];
}

/// Messages marked as read
class MessagesMarkedAsRead extends RealtimeState {
  final String walletId;
  final List<String> messageIds;

  const MessagesMarkedAsRead({
    required this.walletId,
    required this.messageIds,
  });

  @override
  List<Object?> get props => [walletId, messageIds];
}

/// Message reaction added
class MessageReactionAdded extends RealtimeState {
  final String messageId;
  final String emoji;

  const MessageReactionAdded({
    required this.messageId,
    required this.emoji,
  });

  @override
  List<Object?> get props => [messageId, emoji];
}

/// Message reaction removed
class MessageReactionRemoved extends RealtimeState {
  final String messageId;
  final String emoji;

  const MessageReactionRemoved({
    required this.messageId,
    required this.emoji,
  });

  @override
  List<Object?> get props => [messageId, emoji];
}

/// Realtime error state
class RealtimeError extends RealtimeState {
  final String message;
  final String? code;

  const RealtimeError({
    required this.message,
    this.code,
  });

  @override
  List<Object?> get props => [message, code];
}

/// Connection status changed
class RealtimeConnectionStatusChanged extends RealtimeState {
  final bool isConnected;
  final String? reason;

  const RealtimeConnectionStatusChanged({
    required this.isConnected,
    this.reason,
  });

  @override
  List<Object?> get props => [isConnected, reason];
}

/// Subscription status changed
class RealtimeSubscriptionStatusChanged extends RealtimeState {
  final String channelName;
  final bool isSubscribed;
  final String? error;

  const RealtimeSubscriptionStatusChanged({
    required this.channelName,
    required this.isSubscribed,
    this.error,
  });

  @override
  List<Object?> get props => [channelName, isSubscribed, error];
}
