import 'package:equatable/equatable.dart';
import '../../../core/services/chat_service.dart';

/// Base class for realtime events
abstract class RealtimeEvent extends Equatable {
  const RealtimeEvent();

  @override
  List<Object?> get props => [];
}

/// Initialize realtime subscriptions
class InitializeRealtime extends RealtimeEvent {
  final String userId;

  const InitializeRealtime(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Subscribe to wallet updates
class SubscribeToWallet extends RealtimeEvent {
  final String walletId;

  const SubscribeToWallet(this.walletId);

  @override
  List<Object?> get props => [walletId];
}

/// Unsubscribe from wallet updates
class UnsubscribeFromWallet extends RealtimeEvent {
  final String walletId;

  const UnsubscribeFromWallet(this.walletId);

  @override
  List<Object?> get props => [walletId];
}

/// Subscribe to notifications
class SubscribeToNotifications extends RealtimeEvent {
  final String userId;

  const SubscribeToNotifications(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Subscribe to card updates
class SubscribeToCards extends RealtimeEvent {
  final String userId;

  const SubscribeToCards(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// Subscribe to chat messages
class SubscribeToChat extends RealtimeEvent {
  final String walletId;

  const SubscribeToChat(this.walletId);

  @override
  List<Object?> get props => [walletId];
}

/// Unsubscribe from chat messages
class UnsubscribeFromChat extends RealtimeEvent {
  final String walletId;

  const UnsubscribeFromChat(this.walletId);

  @override
  List<Object?> get props => [walletId];
}

/// Send a chat message
class SendChatMessage extends RealtimeEvent {
  final String walletId;
  final String content;
  final String? replyToId;
  final ChatMessageType type;
  final Map<String, dynamic>? metadata;

  const SendChatMessage({
    required this.walletId,
    required this.content,
    this.replyToId,
    this.type = ChatMessageType.text,
    this.metadata,
  });

  @override
  List<Object?> get props => [walletId, content, replyToId, type, metadata];
}

/// Load chat messages
class LoadChatMessages extends RealtimeEvent {
  final String walletId;
  final int limit;
  final String? before;
  final String? after;

  const LoadChatMessages({
    required this.walletId,
    this.limit = 50,
    this.before,
    this.after,
  });

  @override
  List<Object?> get props => [walletId, limit, before, after];
}

/// Update a chat message
class UpdateChatMessage extends RealtimeEvent {
  final String messageId;
  final String content;
  final Map<String, dynamic>? metadata;

  const UpdateChatMessage({
    required this.messageId,
    required this.content,
    this.metadata,
  });

  @override
  List<Object?> get props => [messageId, content, metadata];
}

/// Delete a chat message
class DeleteChatMessage extends RealtimeEvent {
  final String messageId;

  const DeleteChatMessage(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// React to a message
class ReactToMessage extends RealtimeEvent {
  final String messageId;
  final String emoji;

  const ReactToMessage({
    required this.messageId,
    required this.emoji,
  });

  @override
  List<Object?> get props => [messageId, emoji];
}

/// Remove reaction from a message
class RemoveMessageReaction extends RealtimeEvent {
  final String messageId;
  final String emoji;

  const RemoveMessageReaction({
    required this.messageId,
    required this.emoji,
  });

  @override
  List<Object?> get props => [messageId, emoji];
}

/// Mark messages as read
class MarkMessagesAsRead extends RealtimeEvent {
  final String walletId;
  final List<String> messageIds;

  const MarkMessagesAsRead({
    required this.walletId,
    required this.messageIds,
  });

  @override
  List<Object?> get props => [walletId, messageIds];
}

/// Load unread message count
class LoadUnreadCount extends RealtimeEvent {
  final String walletId;

  const LoadUnreadCount(this.walletId);

  @override
  List<Object?> get props => [walletId];
}

/// Cleanup realtime subscriptions
class CleanupRealtime extends RealtimeEvent {
  const CleanupRealtime();
}

/// Handle incoming realtime wallet event
class HandleWalletRealtimeEvent extends RealtimeEvent {
  final Map<String, dynamic> eventData;

  const HandleWalletRealtimeEvent(this.eventData);

  @override
  List<Object?> get props => [eventData];
}

/// Handle incoming realtime notification event
class HandleNotificationRealtimeEvent extends RealtimeEvent {
  final Map<String, dynamic> eventData;

  const HandleNotificationRealtimeEvent(this.eventData);

  @override
  List<Object?> get props => [eventData];
}

/// Handle incoming realtime card event
class HandleCardRealtimeEvent extends RealtimeEvent {
  final Map<String, dynamic> eventData;

  const HandleCardRealtimeEvent(this.eventData);

  @override
  List<Object?> get props => [eventData];
}

/// Handle incoming realtime message event
class HandleMessageRealtimeEvent extends RealtimeEvent {
  final Map<String, dynamic> eventData;

  const HandleMessageRealtimeEvent(this.eventData);

  @override
  List<Object?> get props => [eventData];
}
