import 'package:equatable/equatable.dart';
import '../../../domain/entities/virtual_card.dart';

abstract class VirtualCardEvent extends Equatable {
  const VirtualCardEvent();

  @override
  List<Object?> get props => [];
}

// Card Management Events
class CreateVirtualCardRequested extends VirtualCardEvent {
  final CreateVirtualCardRequest request;

  const CreateVirtualCardRequested(this.request);

  @override
  List<Object?> get props => [request];
}

class LoadVirtualCardsRequested extends VirtualCardEvent {
  final String? walletId;
  final String? userId;

  const LoadVirtualCardsRequested({this.walletId, this.userId});

  @override
  List<Object?> get props => [walletId, userId];
}

class LoadVirtualCardRequested extends VirtualCardEvent {
  final String cardId;

  const LoadVirtualCardRequested(this.cardId);

  @override
  List<Object?> get props => [cardId];
}

class UpdateVirtualCardRequested extends VirtualCardEvent {
  final UpdateVirtualCardRequest request;

  const UpdateVirtualCardRequested(this.request);

  @override
  List<Object?> get props => [request];
}

class DeleteVirtualCardRequested extends VirtualCardEvent {
  final String cardId;

  const DeleteVirtualCardRequested(this.cardId);

  @override
  List<Object?> get props => [cardId];
}

// Card Status Events
class ActivateCardRequested extends VirtualCardEvent {
  final String cardId;

  const ActivateCardRequested(this.cardId);

  @override
  List<Object?> get props => [cardId];
}

class DeactivateCardRequested extends VirtualCardEvent {
  final String cardId;

  const DeactivateCardRequested(this.cardId);

  @override
  List<Object?> get props => [cardId];
}

class CancelCardRequested extends VirtualCardEvent {
  final String cardId;

  const CancelCardRequested(this.cardId);

  @override
  List<Object?> get props => [cardId];
}

// Spending Control Events
class CreateSpendingControlRequested extends VirtualCardEvent {
  final CreateSpendingControlRequest request;

  const CreateSpendingControlRequested(this.request);

  @override
  List<Object?> get props => [request];
}

class LoadSpendingControlsRequested extends VirtualCardEvent {
  final String cardId;

  const LoadSpendingControlsRequested(this.cardId);

  @override
  List<Object?> get props => [cardId];
}

class UpdateSpendingControlRequested extends VirtualCardEvent {
  final String controlId;
  final CreateSpendingControlRequest request;

  const UpdateSpendingControlRequested(this.controlId, this.request);

  @override
  List<Object?> get props => [controlId, request];
}

class DeleteSpendingControlRequested extends VirtualCardEvent {
  final String controlId;

  const DeleteSpendingControlRequested(this.controlId);

  @override
  List<Object?> get props => [controlId];
}

// Card Details Events
class LoadCardDetailsRequested extends VirtualCardEvent {
  final String cardId;

  const LoadCardDetailsRequested(this.cardId);

  @override
  List<Object?> get props => [cardId];
}

// Transaction Authorization Events
class AuthorizeTransactionRequested extends VirtualCardEvent {
  final String cardId;
  final double amount;
  final String currency;
  final String merchantName;
  final String merchantCategory;
  final Map<String, dynamic>? metadata;

  const AuthorizeTransactionRequested({
    required this.cardId,
    required this.amount,
    required this.currency,
    required this.merchantName,
    required this.merchantCategory,
    this.metadata,
  });

  @override
  List<Object?> get props => [cardId, amount, currency, merchantName, merchantCategory, metadata];
}

// Reset Event
class VirtualCardReset extends VirtualCardEvent {
  const VirtualCardReset();
}
