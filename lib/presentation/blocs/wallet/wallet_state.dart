import 'package:equatable/equatable.dart';
import '../../../domain/entities/wallet.dart';
import '../../../domain/entities/invitation.dart';

abstract class WalletState extends Equatable {
  const WalletState();

  @override
  List<Object?> get props => [];
}

class WalletInitial extends WalletState {}

class WalletLoading extends WalletState {}

class WalletsLoaded extends WalletState {
  final List<Wallet> wallets;

  const WalletsLoaded(this.wallets);

  @override
  List<Object> get props => [wallets];
}

class WalletLoaded extends WalletState {
  final Wallet wallet;

  const WalletLoaded(this.wallet);

  @override
  List<Object> get props => [wallet];
}

class WalletCreated extends WalletState {
  final Wallet wallet;

  const WalletCreated(this.wallet);

  @override
  List<Object> get props => [wallet];
}

class WalletUpdated extends WalletState {
  final Wallet wallet;

  const WalletUpdated(this.wallet);

  @override
  List<Object> get props => [wallet];
}

class WalletDeleted extends WalletState {}

class WalletJoined extends WalletState {
  final Wallet wallet;

  const WalletJoined(this.wallet);

  @override
  List<Object> get props => [wallet];
}

class InvitationCreated extends WalletState {
  final Invitation invitation;

  const InvitationCreated(this.invitation);

  @override
  List<Object> get props => [invitation];
}

class WalletInvitationsLoaded extends WalletState {
  final List<Invitation> invitations;

  const WalletInvitationsLoaded(this.invitations);

  @override
  List<Object> get props => [invitations];
}

class InvitationAccepted extends WalletState {
  final Wallet wallet;

  const InvitationAccepted(this.wallet);

  @override
  List<Object> get props => [wallet];
}

class InvitationCancelled extends WalletState {}

class WalletMembersLoaded extends WalletState {
  final List<WalletMember> members;

  const WalletMembersLoaded(this.members);

  @override
  List<Object> get props => [members];
}

class MemberRoleUpdated extends WalletState {
  final WalletMember member;

  const MemberRoleUpdated(this.member);

  @override
  List<Object> get props => [member];
}

class MemberPermissionsUpdated extends WalletState {
  final WalletMember member;

  const MemberPermissionsUpdated(this.member);

  @override
  List<Object> get props => [member];
}

class MemberRemoved extends WalletState {}

class WalletSettingsUpdated extends WalletState {
  final WalletSettings settings;

  const WalletSettingsUpdated(this.settings);

  @override
  List<Object> get props => [settings];
}

class WalletError extends WalletState {
  final String message;

  const WalletError(this.message);

  @override
  List<Object> get props => [message];
}
