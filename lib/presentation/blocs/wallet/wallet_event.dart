import 'package:equatable/equatable.dart';
import '../../../domain/entities/wallet.dart';
import '../../../domain/usecases/wallet/create_wallet_usecase.dart';
import '../../../domain/usecases/wallet/invitation_usecase.dart';
import '../../../domain/usecases/wallet/member_management_usecase.dart';

abstract class WalletEvent extends Equatable {
  const WalletEvent();

  @override
  List<Object?> get props => [];
}

class LoadWallets extends WalletEvent {}

class LoadWallet extends WalletEvent {
  final String walletId;

  const LoadWallet(this.walletId);

  @override
  List<Object> get props => [walletId];
}

class CreateWallet extends WalletEvent {
  final CreateWalletRequest request;

  const CreateWallet(this.request);

  @override
  List<Object> get props => [request];
}

class UpdateWallet extends WalletEvent {
  final String walletId;
  final CreateWalletRequest request;

  const UpdateWallet(this.walletId, this.request);

  @override
  List<Object> get props => [walletId, request];
}

class DeleteWallet extends WalletEvent {
  final String walletId;

  const DeleteWallet(this.walletId);

  @override
  List<Object> get props => [walletId];
}

class JoinWallet extends WalletEvent {
  final String inviteCode;

  const JoinWallet(this.inviteCode);

  @override
  List<Object> get props => [inviteCode];
}

class CreateInvitation extends WalletEvent {
  final CreateInvitationRequest request;

  const CreateInvitation(this.request);

  @override
  List<Object> get props => [request];
}

class LoadWalletInvitations extends WalletEvent {
  final String walletId;

  const LoadWalletInvitations(this.walletId);

  @override
  List<Object> get props => [walletId];
}

class AcceptInvitation extends WalletEvent {
  final String inviteCode;

  const AcceptInvitation(this.inviteCode);

  @override
  List<Object> get props => [inviteCode];
}

class CancelInvitation extends WalletEvent {
  final String invitationId;

  const CancelInvitation(this.invitationId);

  @override
  List<Object> get props => [invitationId];
}

class LoadWalletMembers extends WalletEvent {
  final String walletId;

  const LoadWalletMembers(this.walletId);

  @override
  List<Object> get props => [walletId];
}

class UpdateMemberRole extends WalletEvent {
  final String walletId;
  final String userId;
  final MemberRole role;

  const UpdateMemberRole(this.walletId, this.userId, this.role);

  @override
  List<Object> get props => [walletId, userId, role];
}

class UpdateMemberPermissions extends WalletEvent {
  final String walletId;
  final String userId;
  final UpdateMemberPermissionsRequest request;

  const UpdateMemberPermissions(this.walletId, this.userId, this.request);

  @override
  List<Object> get props => [walletId, userId, request];
}

class RemoveMember extends WalletEvent {
  final String walletId;
  final String userId;

  const RemoveMember(this.walletId, this.userId);

  @override
  List<Object> get props => [walletId, userId];
}

class UpdateWalletSettings extends WalletEvent {
  final String walletId;
  final WalletSettings settings;

  const UpdateWalletSettings(this.walletId, this.settings);

  @override
  List<Object> get props => [walletId, settings];
}
