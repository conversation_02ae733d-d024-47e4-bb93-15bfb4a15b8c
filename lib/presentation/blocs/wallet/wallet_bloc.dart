import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/wallet/create_wallet_usecase.dart';
import '../../../domain/usecases/wallet/get_wallets_usecase.dart';
import '../../../domain/usecases/wallet/join_wallet_usecase.dart';
import '../../../domain/usecases/wallet/invitation_usecase.dart';
import '../../../domain/usecases/wallet/member_management_usecase.dart';
import '../../../core/usecases/usecase.dart';
import 'wallet_event.dart';
import 'wallet_state.dart';

class WalletBloc extends Bloc<WalletEvent, WalletState> {
  final CreateWalletUseCase createWalletUseCase;
  final GetWalletsUseCase getWalletsUseCase;
  final GetWalletUseCase getWalletUseCase;
  final JoinWalletUseCase joinWalletUseCase;
  final CreateInvitationUseCase createInvitationUseCase;
  final GetInvitationsUseCase getInvitationsUseCase;
  final AcceptInvitationUseCase acceptInvitationUseCase;
  final CancelInvitationUseCase cancelInvitationUseCase;
  final GetWalletMembersUseCase getWalletMembersUseCase;
  final UpdateMemberRoleUseCase updateMemberRoleUseCase;
  final UpdateMemberPermissionsUseCase updateMemberPermissionsUseCase;
  final RemoveMemberUseCase removeMemberUseCase;

  WalletBloc({
    required this.createWalletUseCase,
    required this.getWalletsUseCase,
    required this.getWalletUseCase,
    required this.joinWalletUseCase,
    required this.createInvitationUseCase,
    required this.getInvitationsUseCase,
    required this.acceptInvitationUseCase,
    required this.cancelInvitationUseCase,
    required this.getWalletMembersUseCase,
    required this.updateMemberRoleUseCase,
    required this.updateMemberPermissionsUseCase,
    required this.removeMemberUseCase,
  }) : super(WalletInitial()) {
    on<LoadWallets>(_onLoadWallets);
    on<LoadWallet>(_onLoadWallet);
    on<CreateWallet>(_onCreateWallet);
    on<JoinWallet>(_onJoinWallet);
    on<CreateInvitation>(_onCreateInvitation);
    on<LoadWalletInvitations>(_onLoadWalletInvitations);
    on<AcceptInvitation>(_onAcceptInvitation);
    on<CancelInvitation>(_onCancelInvitation);
    on<LoadWalletMembers>(_onLoadWalletMembers);
    on<UpdateMemberRole>(_onUpdateMemberRole);
    on<UpdateMemberPermissions>(_onUpdateMemberPermissions);
    on<RemoveMember>(_onRemoveMember);
  }

  Future<void> _onLoadWallets(LoadWallets event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await getWalletsUseCase(NoParams());
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (wallets) => emit(WalletsLoaded(wallets)),
    );
  }

  Future<void> _onLoadWallet(LoadWallet event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await getWalletUseCase(GetWalletParams(event.walletId));
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (wallet) => emit(WalletLoaded(wallet)),
    );
  }

  Future<void> _onCreateWallet(CreateWallet event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await createWalletUseCase(CreateWalletParams(event.request));
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (wallet) => emit(WalletCreated(wallet)),
    );
  }

  Future<void> _onJoinWallet(JoinWallet event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await joinWalletUseCase(JoinWalletParams(event.inviteCode));
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (wallet) => emit(WalletJoined(wallet)),
    );
  }

  Future<void> _onCreateInvitation(CreateInvitation event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await createInvitationUseCase(CreateInvitationParams(event.request));
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (invitation) => emit(InvitationCreated(invitation)),
    );
  }

  Future<void> _onLoadWalletInvitations(LoadWalletInvitations event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await getInvitationsUseCase(GetInvitationsParams(event.walletId));
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (invitations) => emit(WalletInvitationsLoaded(invitations)),
    );
  }

  Future<void> _onAcceptInvitation(AcceptInvitation event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await acceptInvitationUseCase(AcceptInvitationParams(event.inviteCode));
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (wallet) => emit(InvitationAccepted(wallet)),
    );
  }

  Future<void> _onCancelInvitation(CancelInvitation event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await cancelInvitationUseCase(CancelInvitationParams(event.invitationId));
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (_) => emit(InvitationCancelled()),
    );
  }

  Future<void> _onLoadWalletMembers(LoadWalletMembers event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await getWalletMembersUseCase(GetWalletMembersParams(event.walletId));
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (members) => emit(WalletMembersLoaded(members)),
    );
  }

  Future<void> _onUpdateMemberRole(UpdateMemberRole event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await updateMemberRoleUseCase(
      UpdateMemberRoleParams(event.walletId, event.userId, event.role),
    );
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (member) => emit(MemberRoleUpdated(member)),
    );
  }

  Future<void> _onUpdateMemberPermissions(UpdateMemberPermissions event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await updateMemberPermissionsUseCase(
      UpdateMemberPermissionsParams(event.walletId, event.userId, event.request),
    );
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (member) => emit(MemberPermissionsUpdated(member)),
    );
  }

  Future<void> _onRemoveMember(RemoveMember event, Emitter<WalletState> emit) async {
    emit(WalletLoading());
    
    final result = await removeMemberUseCase(
      RemoveMemberParams(event.walletId, event.userId),
    );
    
    result.fold(
      (failure) => emit(WalletError(failure.message)),
      (_) => emit(MemberRemoved()),
    );
  }
}
