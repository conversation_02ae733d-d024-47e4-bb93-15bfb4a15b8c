part of 'payment_bloc.dart';

enum PaymentStatus {
  initial,
  loading,
  paymentIntentCreated,
  processing,
  success,
  failure,
  customerCreated,
}

class PaymentState extends Equatable {
  final PaymentStatus status;
  final PaymentIntent? paymentIntent;
  final StripeCustomer? customer;
  final String? errorMessage;

  const PaymentState({
    this.status = PaymentStatus.initial,
    this.paymentIntent,
    this.customer,
    this.errorMessage,
  });

  PaymentState copyWith({
    PaymentStatus? status,
    PaymentIntent? paymentIntent,
    StripeCustomer? customer,
    String? errorMessage,
  }) {
    return PaymentState(
      status: status ?? this.status,
      paymentIntent: paymentIntent ?? this.paymentIntent,
      customer: customer ?? this.customer,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, paymentIntent, customer, errorMessage];
}
