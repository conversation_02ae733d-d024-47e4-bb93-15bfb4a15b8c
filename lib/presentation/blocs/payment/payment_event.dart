part of 'payment_bloc.dart';

abstract class PaymentEvent extends Equatable {
  const PaymentEvent();

  @override
  List<Object?> get props => [];
}

class CreatePaymentIntentRequested extends PaymentEvent {
  final CreatePaymentIntentRequest request;

  const CreatePaymentIntentRequested(this.request);

  @override
  List<Object?> get props => [request];
}

class ProcessPaymentRequested extends PaymentEvent {
  final String paymentIntentId;
  final String clientSecret;
  final String? customerId;
  final String? customerEphemeralKeySecret;
  final String? merchantDisplayName;

  const ProcessPaymentRequested({
    required this.paymentIntentId,
    required this.clientSecret,
    this.customerId,
    this.customerEphemeralKeySecret,
    this.merchantDisplayName,
  });

  @override
  List<Object?> get props => [
        paymentIntentId,
        clientSecret,
        customerId,
        customerEphemeralKeySecret,
        merchantDisplayName,
      ];
}

class CreateCustomerRequested extends PaymentEvent {
  final CreateCustomerRequest request;

  const CreateCustomerRequested(this.request);

  @override
  List<Object?> get props => [request];
}

class PaymentReset extends PaymentEvent {
  const PaymentReset();
}
