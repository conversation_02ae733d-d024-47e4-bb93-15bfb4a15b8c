import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../domain/entities/payment.dart';
import '../../../domain/usecases/payment/create_payment_intent_usecase.dart';
import '../../../domain/usecases/payment/process_payment_usecase.dart';
import '../../../domain/usecases/payment/create_customer_usecase.dart';

part 'payment_event.dart';
part 'payment_state.dart';

class PaymentBloc extends Bloc<PaymentEvent, PaymentState> {
  final CreatePaymentIntentUseCase _createPaymentIntentUseCase;
  final ProcessPaymentUseCase _processPaymentUseCase;
  final CreateCustomerUseCase _createCustomerUseCase;

  PaymentBloc(
    this._createPaymentIntentUseCase,
    this._processPaymentUseCase,
    this._createCustomerUseCase,
  ) : super(const PaymentState()) {
    on<CreatePaymentIntentRequested>(_onCreatePaymentIntentRequested);
    on<ProcessPaymentRequested>(_onProcessPaymentRequested);
    on<CreateCustomerRequested>(_onCreateCustomerRequested);
    on<PaymentReset>(_onPaymentReset);
  }

  Future<void> _onCreatePaymentIntentRequested(
    CreatePaymentIntentRequested event,
    Emitter<PaymentState> emit,
  ) async {
    emit(state.copyWith(status: PaymentStatus.loading));

    final result = await _createPaymentIntentUseCase(
      CreatePaymentIntentParams(request: event.request),
    );

    result.fold(
      (failure) => emit(state.copyWith(
        status: PaymentStatus.failure,
        errorMessage: failure.message,
      )),
      (paymentIntent) => emit(state.copyWith(
        status: PaymentStatus.paymentIntentCreated,
        paymentIntent: paymentIntent,
      )),
    );
  }

  Future<void> _onProcessPaymentRequested(
    ProcessPaymentRequested event,
    Emitter<PaymentState> emit,
  ) async {
    emit(state.copyWith(status: PaymentStatus.processing));

    final result = await _processPaymentUseCase(
      ProcessPaymentParams(
        paymentIntentId: event.paymentIntentId,
        clientSecret: event.clientSecret,
        customerId: event.customerId,
        customerEphemeralKeySecret: event.customerEphemeralKeySecret,
        merchantDisplayName: event.merchantDisplayName,
      ),
    );

    result.fold(
      (failure) => emit(state.copyWith(
        status: PaymentStatus.failure,
        errorMessage: failure.message,
      )),
      (paymentIntent) => emit(state.copyWith(
        status: PaymentStatus.success,
        paymentIntent: paymentIntent,
      )),
    );
  }

  Future<void> _onCreateCustomerRequested(
    CreateCustomerRequested event,
    Emitter<PaymentState> emit,
  ) async {
    emit(state.copyWith(status: PaymentStatus.loading));

    final result = await _createCustomerUseCase(
      CreateCustomerParams(request: event.request),
    );

    result.fold(
      (failure) => emit(state.copyWith(
        status: PaymentStatus.failure,
        errorMessage: failure.message,
      )),
      (customer) => emit(state.copyWith(
        status: PaymentStatus.customerCreated,
        customer: customer,
      )),
    );
  }

  void _onPaymentReset(PaymentReset event, Emitter<PaymentState> emit) {
    emit(const PaymentState());
  }
}
